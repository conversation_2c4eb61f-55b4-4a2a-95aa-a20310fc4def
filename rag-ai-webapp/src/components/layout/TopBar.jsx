import { useState } from 'react';
import '../../styles/topbar.css';
import logoImage from '../../assets/images/logo.png';

const TopBar = ({ darkMode, toggleDarkMode, activeAgent, setActiveAgent, activeSection, setActiveSection }) => {
  // Sample agents data
  const [agents, setAgents] = useState([
    {
      id: 'a1',
      name: 'Assistente Supporto',
      avatar: '/avatar-a1.svg',
      color: '#7122ff',
    },
    {
      id: 'a2',
      name: '<PERSON><PERSON><PERSON>',
      avatar: '/avatar-a2.svg',
      color: '#2a9d8f',
    },
    {
      id: 'a3',
      name: 'Ricerca Documenti',
      avatar: '/avatar-a3.svg',
      color: '#e76f51',
    },
    {
      id: 'a4',
      name: 'Knowledge Base',
      avatar: '/avatar-a4.svg',
      color: '#4a6cf7',
    },
  ]);

  return (
    <div className="top-bar">
      <div className="left-section">
        <div className="logo">
          <img src={logoImage} alt="iCodium Logo" className="logo-image" />
        </div>

        <div className="action-buttons">
          <button
            className={`action-button ${activeSection === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveSection('chat')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
            Chat
          </button>

          <button
            className={`action-button ${activeSection === 'brain' ? 'active' : ''}`}
            onClick={() => setActiveSection('brain')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
            Knowledge
          </button>

          <button
            className={`action-button ${activeSection === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveSection('settings')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            Impostazioni
          </button>
        </div>

        <div className="separator"></div>
      </div>

      <div className="agents-section">
        <div className="agent-icons">
          {agents.map(agent => (
            <div
              key={agent.id}
              className={`agent-icon ${activeAgent === agent.id ? 'active' : ''}`}
              onClick={() => setActiveAgent(agent.id)}
              style={{ borderColor: activeAgent === agent.id ? agent.color : '' }}
              title={agent.name}
            >
              {agent.avatar ? (
                <img src={agent.avatar} alt={agent.name} />
              ) : (
                <div style={{ backgroundColor: agent.color, width: '100%', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>
                  {agent.name.charAt(0)}
                </div>
              )}
            </div>
          ))}

          <div className="add-agent-btn" title="Aggiungi nuovo agente">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </div>
        </div>
      </div>

      <div className="right-section">
        <button className="theme-toggle" onClick={toggleDarkMode} title={darkMode ? 'Passa al tema chiaro' : 'Passa al tema scuro'}>
          {darkMode ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
          )}
        </button>

        <div className="user-profile">
          <div className="user-avatar">
            <img src="/user-avatar.svg" alt="User" onError={(e) => { e.target.onerror = null; e.target.style.display = 'none'; e.target.parentNode.textContent = 'U'; }} />
          </div>
          <div className="user-name">Utente</div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
