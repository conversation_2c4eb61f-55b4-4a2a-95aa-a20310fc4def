import { useState, useRef, useEffect } from 'react';
// import '../../styles/sidebar.css';

const Sidebar = ({ activeSection, setActiveSection, activeChat, setActiveChat }) => {
  // Sample favorite chats
  const [favoriteChats, setFavoriteChats] = useState([
    { id: 'fav1', name: 'Progetto importante', isFavorite: true, date: '2023-06-10T14:30:00' },
    { id: 'fav2', name: 'Ricerca AI', isFavorite: true, date: '2023-06-12T09:15:00' },
    { id: 'fav3', name: '<PERSON><PERSON><PERSON> dati clienti', isFavorite: true, date: '2023-06-14T11:20:00' },
  ]);

  // Drag and drop state
  const [draggedChat, setDraggedChat] = useState(null);
  const [dragOverFolder, setDragOverFolder] = useState(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [timeFilter, setTimeFilter] = useState('all'); // 'all', 'today', 'week'
  const newFolderInputRef = useRef(null);

  // Sample chat folders
  const [folders, setFolders] = useState([
    {
      id: '1',
      name: 'Progetti di lavoro',
      isOpen: true,
      children: [
        { id: '1-1', name: 'Pianificazione Q3', type: 'chat', isFavorite: false, date: '2023-06-16T09:30:00' },
        { id: '1-2', name: 'Supporto clienti', type: 'chat', isFavorite: false, date: '2023-06-15T10:30:00' },
        { id: '1-3', name: 'Analisi dati Q2', type: 'chat', isFavorite: false, date: '2023-06-14T16:45:00' },
        { id: '1-4', name: 'Riunione marketing', type: 'chat', isFavorite: false, date: '2023-06-13T11:20:00' },
        { id: '1-5', name: 'Strategia social media', type: 'chat', isFavorite: false, date: '2023-06-12T14:15:00' },
      ]
    },
    {
      id: '2',
      name: 'Personale',
      isOpen: false,
      children: [
        { id: '2-1', name: 'Idee vacanze', type: 'chat', isFavorite: false, date: new Date().toISOString() },
        { id: '2-2', name: 'Ricette', type: 'chat', isFavorite: false, date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() },
        { id: '2-3', name: 'Appunti corso', type: 'chat', isFavorite: false, date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() },
      ]
    },
    {
      id: '3',
      name: 'Ricerca',
      isOpen: false,
      children: [
        { id: '3-1', name: 'Intelligenza artificiale', type: 'chat', isFavorite: false, date: '2023-06-10T08:30:00' },
        { id: '3-2', name: 'Machine Learning', type: 'chat', isFavorite: false, date: '2023-06-09T15:45:00' },
        { id: '3-3', name: 'Deep Learning', type: 'chat', isFavorite: false, date: '2023-06-08T11:20:00' },
        { id: '3-4', name: 'Computer Vision', type: 'chat', isFavorite: false, date: '2023-06-07T09:15:00' },
      ]
    },
  ]);

  // Focus on new folder input when it appears
  useEffect(() => {
    if (isCreatingFolder && newFolderInputRef.current) {
      newFolderInputRef.current.focus();
    }
  }, [isCreatingFolder]);

  // Toggle folder open/closed
  const toggleFolder = (folderId) => {
    setFolders(folders.map(folder => {
      if (folder.id === folderId) {
        return { ...folder, isOpen: !folder.isOpen };
      }
      return folder;
    }));
  };

  // Handle drag start
  const handleDragStart = (e, chat, folderId) => {
    setDraggedChat({
      ...chat,
      folderId: folderId || chat.folderId
    });
    e.dataTransfer.setData('text/plain', chat.id);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e, folderId) => {
    e.preventDefault();
    setDragOverFolder(folderId);
  };

  // Handle drop
  const handleDrop = (e, targetFolderId) => {
    e.preventDefault();

    if (!draggedChat) return;

    const { id, name, type, isFavorite, date, folderId: sourceFolderId } = draggedChat;

    // If dropping in the same folder, do nothing
    if (sourceFolderId === targetFolderId) {
      setDraggedChat(null);
      setDragOverFolder(null);
      return;
    }

    // Remove from source folder
    if (sourceFolderId) {
      setFolders(folders.map(folder => {
        if (folder.id === sourceFolderId) {
          return {
            ...folder,
            children: folder.children.filter(child => child.id !== id)
          };
        }
        return folder;
      }));
    } else {
      // If dragging from favorites, remove from favorites
      setFavoriteChats(favoriteChats.filter(chat => chat.id !== id));
    }

    // Add to target folder
    setFolders(folders.map(folder => {
      if (folder.id === targetFolderId) {
        return {
          ...folder,
          children: [...folder.children, { id, name, type, isFavorite, date }]
        };
      }
      return folder;
    }));

    setDraggedChat(null);
    setDragOverFolder(null);
  };

  // Filter chats by time
  const filterChatsByTime = (chats) => {
    if (timeFilter === 'all') return chats;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const weekAgo = today - 7 * 24 * 60 * 60 * 1000;

    return chats.filter(chat => {
      const chatDate = new Date(chat.date).getTime();
      if (timeFilter === 'today') {
        return chatDate >= today;
      } else if (timeFilter === 'week') {
        return chatDate >= weekAgo;
      }
      return true;
    });
  };

  // Create new folder
  const createNewFolder = () => {
    if (!newFolderName.trim()) return;

    const newFolder = {
      id: `folder-${Date.now()}`,
      name: newFolderName,
      isOpen: true,
      children: []
    };

    setFolders([...folders, newFolder]);
    setIsCreatingFolder(false);
    setNewFolderName('');
  };

  // Toggle favorite status
  const toggleFavorite = (chatId, inFolder = false) => {
    if (inFolder) {
      // Update chat in folders
      const updatedFolders = folders.map(folder => {
        const updatedChildren = folder.children.map(child => {
          if (child.id === chatId) {
            const updatedChild = { ...child, isFavorite: !child.isFavorite };

            // If adding to favorites, add to favorites list
            if (updatedChild.isFavorite) {
              setFavoriteChats([...favoriteChats, updatedChild]);
            } else {
              // If removing from favorites, remove from favorites list
              setFavoriteChats(favoriteChats.filter(chat => chat.id !== chatId));
            }

            return updatedChild;
          }
          return child;
        });

        return { ...folder, children: updatedChildren };
      });

      setFolders(updatedFolders);
    } else {
      // Update chat in favorites
      const updatedFavorites = favoriteChats.map(chat => {
        if (chat.id === chatId) {
          return { ...chat, isFavorite: !chat.isFavorite };
        }
        return chat;
      });

      // Filter out non-favorites
      const filteredFavorites = updatedFavorites.filter(chat => chat.isFavorite);
      setFavoriteChats(filteredFavorites);
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 className="sidebar-title">Chat</h2>
        <p className="sidebar-subtitle">Le tue conversazioni</p>
      </div>

      <div className="sidebar-content">
        <button className="btn btn-primary" style={{ width: '100%', marginBottom: 'var(--space-6)' }} onClick={() => {
          setActiveChat(null);
          setActiveSection('chat');
        }}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Nuova chat
        </button>

        {/* Favorites Section */}
        <div className="sidebar-section">
          <div className="sidebar-section-header">
            <h3 className="sidebar-section-title">Preferiti</h3>
          </div>

          <div className="chat-list">
            {favoriteChats.map(chat => (
              <div
                key={chat.id}
                className={`chat-item ${activeChat === chat.id ? 'active' : ''}`}
                onClick={() => {
                  setActiveChat(chat.id);
                  setActiveSection('chat');
                }}
                draggable="true"
                onDragStart={(e) => handleDragStart(e, chat)}
              >
                <div className="chat-item-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                  </svg>
                </div>
                <div className="chat-item-content">
                  <h4 className="chat-item-title">{chat.name}</h4>
                  <p className="chat-item-preview">Conversazione preferita</p>
                </div>
                <div className="chat-item-actions">
                  <button
                    className="chat-item-action active"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(chat.id);
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chats Section */}
        <div className="sidebar-section">
          <div className="sidebar-section-header">
            <h3 className="sidebar-section-title">Conversazioni</h3>
            <button className="sidebar-section-action" onClick={() => setIsCreatingFolder(true)}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </div>

          <div style={{ marginBottom: 'var(--space-4)' }}>
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="btn btn-ghost"
              style={{ width: '100%', textAlign: 'left' }}
            >
              <option value="all">Tutte le conversazioni</option>
              <option value="today">Solo oggi</option>
              <option value="week">Ultimi 7 giorni</option>
            </select>
          </div>

          {isCreatingFolder && (
            <div className="new-folder-input-container">
              <input
                type="text"
                ref={newFolderInputRef}
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="Nome cartella"
                className="new-folder-input"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    createNewFolder();
                  } else if (e.key === 'Escape') {
                    setIsCreatingFolder(false);
                    setNewFolderName('');
                  }
                }}
              />
              <div className="new-folder-actions">
                <button onClick={createNewFolder} className="confirm-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </button>
                <button onClick={() => {
                  setIsCreatingFolder(false);
                  setNewFolderName('');
                }} className="cancel-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
            </div>
          )}

          <div>
            {folders.map(folder => (
              <div
                key={folder.id}
                className={`folder-item ${folder.isOpen ? 'expanded' : ''} ${dragOverFolder === folder.id ? 'drag-over' : ''}`}
                onDragOver={(e) => handleDragOver(e, folder.id)}
                onDrop={(e) => handleDrop(e, folder.id)}
              >
                <div className="folder-header" onClick={() => toggleFolder(folder.id)}>
                  <svg className="folder-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                  <span className="folder-title">{folder.name}</span>
                  <span className="folder-count">{folder.children.length}</span>
                </div>

                {folder.isOpen && (
                  <div className="folder-content">
                    {filterChatsByTime(folder.children).map(child => (
                      <div
                        key={child.id}
                        className={`chat-item ${activeChat === child.id ? 'active' : ''}`}
                        onClick={() => {
                          setActiveChat(child.id);
                          setActiveSection('chat');
                        }}
                        draggable="true"
                        onDragStart={(e) => handleDragStart(e, child, folder.id)}
                      >
                        <div className="chat-item-icon">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                          </svg>
                        </div>
                        <div className="chat-item-content">
                          <h4 className="chat-item-title">{child.name}</h4>
                          <p className="chat-item-preview">Ultima attività</p>
                        </div>
                        <div className="chat-item-actions">
                          <button
                            className={`chat-item-action ${child.isFavorite ? 'active' : ''}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleFavorite(child.id, true);
                            }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
