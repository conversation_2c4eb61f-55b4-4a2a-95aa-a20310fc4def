import React, { useState } from 'react';
import '../../styles/agent-detail.css';

const CreateAgent = ({ onSave, onCancel, parentAgent }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'assistant',
    avatar: null,
    model: 'icodium-agata-1',
    config: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9,
      frequencyPenalty: 0.0,
      presencePenalty: 0.0,
      responseFormat: 'auto',
      contextWindow: 'medium',
      knowledgeWeight: 0.8,
      systemPrompt: ''
    }
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const [errors, setErrors] = useState({});

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type } = e.target;

    if (name.startsWith('config.')) {
      // Handle config fields
      const configField = name.split('.')[1];
      const processedValue = type === 'number' || type === 'range'
        ? parseFloat(value)
        : value;

      setFormData({
        ...formData,
        config: {
          ...formData.config,
          [configField]: processedValue
        }
      });
    } else {
      // Handle regular fields
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Get model description based on selected model
  const getModelDescription = (modelId) => {
    switch(modelId) {
      case 'icodium-agata-1':
        return 'Modello bilanciato con ottime capacità di comprensione e ragionamento. Ideale per la maggior parte delle applicazioni.';
      case 'icodium-scorpion-v2':
        return 'Modello ottimizzato per velocità e efficienza. Eccellente per applicazioni che richiedono risposte rapide.';
      case 'icodium-maverick':
        return 'Modello avanzato con capacità superiori di ragionamento e creatività. Ideale per compiti complessi.';
      default:
        return '';
    }
  };

  // Handle model selection
  const handleModelSelect = (modelId) => {
    // Update model and set appropriate default config values
    let defaultConfig = { ...formData.config };

    switch(modelId) {
      case 'icodium-agata-1':
        defaultConfig = {
          ...defaultConfig,
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: `Sei un assistente AI chiamato ${formData.name || 'Assistente'}. Il tuo compito è fornire risposte accurate e utili basate sulla knowledge base a tua disposizione.`
        };
        break;
      case 'icodium-scorpion-v2':
        defaultConfig = {
          ...defaultConfig,
          temperature: 0.5,
          maxTokens: 1024,
          systemPrompt: `Sei un assistente AI veloce ed efficiente chiamato ${formData.name || 'Assistente'}. Fornisci risposte concise e precise basate sulla knowledge base a tua disposizione.`
        };
        break;
      case 'icodium-maverick':
        defaultConfig = {
          ...defaultConfig,
          temperature: 0.9,
          maxTokens: 4096,
          systemPrompt: `Sei un assistente AI avanzato chiamato ${formData.name || 'Assistente'} con capacità superiori di ragionamento e creatività. Analizza a fondo le richieste e fornisci risposte dettagliate e approfondite.`
        };
        break;
    }

    setFormData({
      ...formData,
      model: modelId,
      config: defaultConfig
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'La descrizione è obbligatoria';
    }

    // Update system prompt with actual name if it's still using a placeholder
    const updatedFormData = {
      ...formData,
      config: {
        ...formData.config,
        systemPrompt: formData.config.systemPrompt.replace('Assistente', formData.name)
      }
    };

    setFormData(updatedFormData);

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Save agent
    onSave(updatedFormData);
  };

  return (
    <div className="knowledge-content">
      <div className="knowledge-header">
        <button className="btn btn-secondary" onClick={onCancel}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Indietro
        </button>
        <div className="knowledge-title">
          {parentAgent ? `Crea sottoagente per ${parentAgent.name}` : 'Crea nuovo agente'}
        </div>
      </div>

      <div className="create-agent-form">
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Nome dell'agente*</label>
            <input
              type="text"
              className={`form-input ${errors.name ? 'error' : ''}`}
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Es. Assistente Supporto Tecnico"
            />
            {errors.name && <div className="form-error">{errors.name}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Descrizione*</label>
            <textarea
              className={`form-textarea ${errors.description ? 'error' : ''}`}
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Descrivi lo scopo e le capacità di questo agente"
            ></textarea>
            {errors.description && <div className="form-error">{errors.description}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Tipo di agente</label>
            <select
              className="form-select"
              name="type"
              value={formData.type}
              onChange={handleChange}
            >
              <option value="assistant">Assistente</option>
              <option value="researcher">Ricercatore</option>
              <option value="analyst">Analista</option>
              <option value="custom">Personalizzato</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Avatar (opzionale)</label>
            <div className="upload-area">
              <div className="upload-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </div>
              <div className="upload-text">Trascina un'immagine o clicca per caricare</div>
              <div className="upload-hint">PNG, JPG o GIF, max 2MB</div>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Modello di base*</label>
            <div className="model-selection-wrapper">
              <div className="model-options">
                <div
                  className={`model-option ${formData.model === 'icodium-agata-1' ? 'selected' : ''}`}
                  onClick={() => handleModelSelect('icodium-agata-1')}
                >
                  <div className="model-icon agata">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M12 16v-4"></path>
                      <path d="M12 8h.01"></path>
                    </svg>
                  </div>
                  <div className="model-info">
                    <div className="model-name">iCodium Agata 1</div>
                    <div className="model-desc">Bilanciato e versatile</div>
                  </div>
                </div>

                <div
                  className={`model-option ${formData.model === 'icodium-scorpion-v2' ? 'selected' : ''}`}
                  onClick={() => handleModelSelect('icodium-scorpion-v2')}
                >
                  <div className="model-icon scorpion">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                      <path d="M2 17l10 5 10-5"></path>
                      <path d="M2 12l10 5 10-5"></path>
                    </svg>
                  </div>
                  <div className="model-info">
                    <div className="model-name">iCodium Scorpion V2</div>
                    <div className="model-desc">Veloce ed efficiente</div>
                  </div>
                </div>

                <div
                  className={`model-option ${formData.model === 'icodium-maverick' ? 'selected' : ''}`}
                  onClick={() => handleModelSelect('icodium-maverick')}
                >
                  <div className="model-icon maverick">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                      <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                      <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                      <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                      <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                  </div>
                  <div className="model-info">
                    <div className="model-name">iCodium Maverick</div>
                    <div className="model-desc">Avanzato e potente</div>
                  </div>
                </div>
              </div>
              <div className="model-description-box">
                <p>{getModelDescription(formData.model)}</p>
              </div>
            </div>
          </div>

          <div className="advanced-config-toggle">
            <button
              type="button"
              className="toggle-btn"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'Nascondi configurazione avanzata' : 'Mostra configurazione avanzata'}
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {showAdvanced ?
                  <polyline points="18 15 12 9 6 15"></polyline> :
                  <polyline points="6 9 12 15 18 9"></polyline>}
              </svg>
            </button>
          </div>

          {showAdvanced && (
            <div className="advanced-config-section">
              <h3>Configurazione Avanzata</h3>

              <div className="config-tabs">
                <button className="config-tab active">Parametri</button>
              </div>

              <div className="config-group">
                <label>
                  Temperatura
                  <span className="config-value">{formData.config.temperature.toFixed(1)}</span>
                </label>
                <input
                  type="range"
                  name="config.temperature"
                  min="0"
                  max="2"
                  step="0.1"
                  value={formData.config.temperature}
                  onChange={handleChange}
                  className="slider"
                />
                <div className="slider-labels">
                  <span>Preciso</span>
                  <span>Creativo</span>
                </div>
              </div>

              <div className="config-group">
                <label>
                  Lunghezza massima risposta
                  <span className="config-value">{formData.config.maxTokens}</span>
                </label>
                <select
                  name="config.maxTokens"
                  value={formData.config.maxTokens}
                  onChange={handleChange}
                  className="config-select"
                >
                  <option value="512">Breve (512 tokens)</option>
                  <option value="1024">Medio (1024 tokens)</option>
                  <option value="2048">Lungo (2048 tokens)</option>
                  <option value="4096">Molto lungo (4096 tokens)</option>
                </select>
              </div>

              <div className="config-group">
                <label>
                  Peso della knowledge base
                  <span className="config-value">{formData.config.knowledgeWeight.toFixed(1)}</span>
                </label>
                <input
                  type="range"
                  name="config.knowledgeWeight"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.config.knowledgeWeight}
                  onChange={handleChange}
                  className="slider"
                />
                <div className="slider-labels">
                  <span>Conoscenza generale</span>
                  <span>Knowledge base</span>
                </div>
              </div>

              <div className="config-group full-width">
                <label>System Prompt</label>
                <textarea
                  name="config.systemPrompt"
                  value={formData.config.systemPrompt}
                  onChange={handleChange}
                  rows="5"
                  className="config-textarea"
                  placeholder="Inserisci il system prompt per l'agente..."
                ></textarea>
              </div>
            </div>
          )}

          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>Annulla</button>
            <button type="submit" className="btn btn-primary">
              {parentAgent ? 'Crea sottoagente' : 'Crea agente'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateAgent;
