import React, { useState, useEffect } from 'react';
import '../../styles/add-knowledge.css';

const EditKnowledge = ({ source, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: ''
  });

  const [errors, setErrors] = useState({});

  // Initialize form data when source changes
  useEffect(() => {
    if (source) {
      setFormData({
        name: source.name || '',
        description: source.description || '',
        type: source.type || ''
      });
    }
  }, [source]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Save changes
    onSave({
      ...source,
      name: formData.name,
      description: formData.description,
      type: formData.type
    });
  };

  return (
    <div className="knowledge-content">
      <div className="knowledge-header">
        <button className="btn btn-secondary" onClick={onCancel}>
          <i className="fa fa-arrow-left"></i>
          Indietro
        </button>
        <div className="knowledge-title">Modifica fonte di conoscenza</div>
      </div>

      <div className="add-knowledge-form">
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Nome*</label>
            <input
              type="text"
              className={`form-input ${errors.name ? 'error' : ''}`}
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Es. Manuale di supporto tecnico"
            />
            {errors.name && <div className="form-error">{errors.name}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Descrizione (opzionale)</label>
            <textarea
              className="form-textarea"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Descrivi brevemente questa fonte di conoscenza"
            ></textarea>
          </div>

          <div className="form-group">
            <label className="form-label">Tipo di documento</label>
            <select
              className="form-select"
              name="type"
              value={formData.type}
              onChange={handleChange}
              disabled={source.type === 'webpage' || source.type === 'text'}
            >
              <option value="pdf">PDF</option>
              <option value="docx">DOCX</option>
              <option value="txt">TXT</option>
            </select>
            <div className="form-hint">
              {(source.type === 'webpage' || source.type === 'text') && 
                "Il tipo di documento non può essere modificato per questo tipo di fonte."}
            </div>
          </div>

          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>Annulla</button>
            <button type="submit" className="btn btn-primary">
              <i className="fa fa-save"></i>
              Salva modifiche
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditKnowledge;
