import React, { useState, useEffect } from 'react';
import '../../styles/knowledge-sources.css';
import AddKnowledge from './AddKnowledge';
import EditKnowledge from './EditKnowledge';

const KnowledgeSources = ({ agent, onAddKnowledge }) => {
  const [sources, setSources] = useState([
    {
      id: 1,
      name: 'Manuale di supporto tecnico',
      description: 'Documentazione completa per il supporto tecnico dei prodotti',
      type: 'pdf',
      dateAdded: '2023-05-15',
      size: '2.4 MB',
      chunks: 45,
      tokens: 12500
    },
    {
      id: 2,
      name: 'FAQ del prodotto',
      description: 'Domande frequenti sul prodotto e relative risposte',
      type: 'docx',
      dateAdded: '2023-06-02',
      size: '1.1 MB',
      chunks: 28,
      tokens: 8200
    },
    {
      id: 3,
      name: 'Documentazione API',
      description: 'Documentazione tecnica delle API disponibili',
      type: 'pdf',
      dateAdded: '2023-04-20',
      size: '3.7 MB',
      chunks: 62,
      tokens: 18300
    },
    {
      id: 4,
      name: '<PERSON>uida utente',
      description: 'Guida completa all\'utilizzo del software',
      type: 'pdf',
      dateAdded: '2023-03-10',
      size: '5.2 MB',
      chunks: 78,
      tokens: 24600
    },
    {
      id: 5,
      name: 'Specifiche tecniche',
      description: 'Dettagli tecnici e specifiche del prodotto',
      type: 'docx',
      dateAdded: '2023-05-28',
      size: '0.8 MB',
      chunks: 15,
      tokens: 4200
    }
  ]);

  const [filteredSources, setFilteredSources] = useState(sources);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showSourceDetail, setShowSourceDetail] = useState(false);
  const [selectedSource, setSelectedSource] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  // Update filtered sources when sources, search term, or filter type changes
  useEffect(() => {
    let filtered = [...sources];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(source => 
        source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        source.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(source => source.type === filterType);
    }

    setFilteredSources(filtered);
  }, [sources, searchTerm, filterType]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    setFilterType(e.target.value);
  };

  // Handle source click
  const handleSourceClick = (source) => {
    setSelectedSource(source);
    setShowSourceDetail(true);
  };

  // Handle source edit
  const handleSourceEdit = (e, source) => {
    e.stopPropagation();
    setSelectedSource(source);
    setShowEditForm(true);
    setShowSourceDetail(false);
  };

  // Handle source delete
  const handleSourceDelete = (e, sourceId) => {
    e.stopPropagation();
    
    // Chiedi conferma prima di eliminare
    if (confirm('Sei sicuro di voler eliminare questa fonte di conoscenza?')) {
      // Simulate API call to delete source
      const updatedSources = sources.filter(source => source.id !== sourceId);
      setSources(updatedSources);
      
      // Aggiorna anche i filteredSources
      setFilteredSources(prevFiltered => prevFiltered.filter(source => source.id !== sourceId));
    }
  };

  // Get icon based on source type
  const getSourceIcon = (type) => {
    switch (type) {
      case 'pdf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'docx':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'webpage':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
        );
      case 'text':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
            <polyline points="13 2 13 9 20 9"></polyline>
          </svg>
        );
    }
  };

  // Handle add knowledge
  const handleAddKnowledge = () => {
    setShowAddForm(true);
  };

  // Handle save new knowledge
  const handleSaveNewKnowledge = (newSource) => {
    // Generate a new ID (in a real app, this would be handled by the backend)
    const newId = Math.max(...sources.map(s => s.id)) + 1;
    
    // Add the new source to the list
    const sourceWithId = {
      ...newSource,
      id: newId,
      chunks: Math.floor(Math.random() * 50) + 10, // Simulated data
      tokens: Math.floor(Math.random() * 15000) + 5000 // Simulated data
    };
    
    setSources(prevSources => [...prevSources, sourceWithId]);
    setFilteredSources(prevFiltered => [...prevFiltered, sourceWithId]);
    setShowAddForm(false);
  };

  // Handle save edited knowledge
  const handleSaveEditedKnowledge = (editedSource) => {
    // Update the source in the list
    setSources(prevSources => 
      prevSources.map(source => 
        source.id === editedSource.id ? editedSource : source
      )
    );
    
    setFilteredSources(prevFiltered => 
      prevFiltered.map(source => 
        source.id === editedSource.id ? editedSource : source
      )
    );
    
    setShowEditForm(false);
  };

  // Handle cancel forms
  const handleCancelForm = () => {
    setShowAddForm(false);
    setShowEditForm(false);
  };

  if (showAddForm) {
    return (
      <AddKnowledge 
        agent={agent} 
        onSave={handleSaveNewKnowledge} 
        onCancel={handleCancelForm} 
      />
    );
  }
  
  if (showEditForm && selectedSource) {
    return (
      <EditKnowledge 
        source={selectedSource} 
        onSave={handleSaveEditedKnowledge} 
        onCancel={handleCancelForm} 
      />
    );
  }
  
  return (
    <div className="knowledge-sources-container">
      <div className="knowledge-sources-header">
        <h3 className="knowledge-sources-title">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
          </svg>
          Fonti di conoscenza
        </h3>
        <div className="knowledge-sources-actions">
          <button className="btn-primary" onClick={handleAddKnowledge}>
            <i className="fa fa-plus"></i>
            Aggiungi conoscenza
          </button>
        </div>
      </div>

      <div className="knowledge-sources-filter">
        <div className="filter-input">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <input
            type="text"
            placeholder="Cerca fonti di conoscenza..."
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <select
          className="filter-select"
          value={filterType}
          onChange={handleFilterChange}
        >
          <option value="all">Tutti i tipi</option>
          <option value="pdf">PDF</option>
          <option value="docx">DOCX</option>
          <option value="webpage">Pagina Web</option>
          <option value="text">Testo</option>
        </select>
      </div>

      {filteredSources.length > 0 ? (
        <div className="knowledge-sources">
          {filteredSources.map(source => (
            <div
              key={source.id}
              className="knowledge-source-item"
              onClick={() => handleSourceClick(source)}
            >
              <div className="knowledge-source-icon">
                {getSourceIcon(source.type)}
              </div>
              <div className="knowledge-source-info">
                <div className="knowledge-source-name">{source.name}</div>
                <div className="knowledge-source-description">{source.description}</div>
                <div className="knowledge-source-meta">
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    {source.dateAdded}
                  </div>
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    </svg>
                    {source.size}
                  </div>
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="17" y1="10" x2="3" y2="10"></line>
                      <line x1="21" y1="6" x2="3" y2="6"></line>
                      <line x1="21" y1="14" x2="3" y2="14"></line>
                      <line x1="17" y1="18" x2="3" y2="18"></line>
                    </svg>
                    {source.chunks} chunks
                  </div>
                </div>
              </div>
              <div className="knowledge-source-type">
                {source.type.toUpperCase()}
              </div>
              <div className="knowledge-source-actions">
                <button
                  className="source-action-btn view-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSourceClick(source);
                  }}
                  title="Visualizza dettagli"
                >
                  <i className="fa fa-eye"></i>
                </button>
                <button
                  className="source-action-btn edit-btn"
                  onClick={(e) => handleSourceEdit(e, source)}
                  title="Modifica fonte"
                >
                  <i className="fa fa-pencil"></i>
                </button>
                <button
                  className="source-action-btn delete-btn"
                  onClick={(e) => handleSourceDelete(e, source.id)}
                  title="Elimina fonte"
                >
                  <i className="fa fa-trash"></i>
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="empty-state">
          <div className="empty-state-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
          </div>
          <h3 className="empty-state-title">Nessuna fonte di conoscenza trovata</h3>
          <p className="empty-state-description">
            Aggiungi documenti, pagine web o testo per migliorare la conoscenza dell'agente.
          </p>
          <button className="btn-primary" onClick={handleAddKnowledge}>
            <i className="fa fa-plus"></i>
            Aggiungi conoscenza
          </button>
        </div>
      )}

      {showSourceDetail && selectedSource && (
        <div className="source-detail-modal" onClick={() => setShowSourceDetail(false)}>
          <div className="source-detail-content" onClick={(e) => e.stopPropagation()}>
            <div className="source-detail-header">
              <h3 className="source-detail-title">Dettagli fonte di conoscenza</h3>
              <button
                className="source-detail-close"
                onClick={() => setShowSourceDetail(false)}
              >
                <i className="fa fa-times"></i>
              </button>
            </div>
            <div className="source-detail-body">
              <div className="source-detail-info">
                <div className="source-detail-icon">
                  {getSourceIcon(selectedSource.type)}
                </div>
                <div className="source-detail-meta">
                  <h4 className="source-detail-name">{selectedSource.name}</h4>
                  <p className="source-detail-description">{selectedSource.description}</p>
                </div>
              </div>

              <div className="source-detail-stats">
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  Aggiunto il: {selectedSource.dateAdded}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  </svg>
                  Dimensione: {selectedSource.size}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="17" y1="10" x2="3" y2="10"></line>
                    <line x1="21" y1="6" x2="3" y2="6"></line>
                    <line x1="21" y1="14" x2="3" y2="14"></line>
                    <line x1="17" y1="18" x2="3" y2="18"></line>
                  </svg>
                  Chunks: {selectedSource.chunks}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path>
                    <line x1="16" y1="8" x2="2" y2="22"></line>
                    <line x1="17.5" y1="15" x2="9" y2="15"></line>
                  </svg>
                  Tokens: {selectedSource.tokens}
                </div>
              </div>

              <div className="source-detail-preview">
                <h4 className="source-detail-preview-title">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  Anteprima contenuto
                </h4>
                <div className="source-detail-preview-content">
                  {selectedSource.type === 'text' ?
                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl...' :
                    'Anteprima non disponibile per questo tipo di documento. Il contenuto è stato elaborato e suddiviso in chunks per l\'utilizzo da parte dell\'agente AI.'}
                </div>
              </div>
            </div>
            <div className="source-detail-actions">
              <button
                className="btn-secondary"
                onClick={() => setShowSourceDetail(false)}
              >
                Chiudi
              </button>
              <button
                className="btn-primary"
                onClick={(e) => handleSourceEdit(e, selectedSource)}
              >
                <i className="fa fa-pencil"></i>
                Modifica
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KnowledgeSources;
