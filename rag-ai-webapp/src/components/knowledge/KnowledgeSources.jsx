import React, { useState, useEffect } from 'react';
import '../../styles/knowledge-sources.css';

const KnowledgeSources = ({ agent, onAddKnowledge }) => {
  const [sources, setSources] = useState([]);
  const [filteredSources, setFilteredSources] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedSource, setSelectedSource] = useState(null);
  const [showSourceDetail, setShowSourceDetail] = useState(false);

  // Simulated data for demo purposes
  useEffect(() => {
    // Simulate API call to get knowledge sources
    const demoSources = [
      {
        id: 1,
        name: 'Manuale tecnico iCodium',
        description: 'Documentazione tecnica completa per l\'utilizzo della piattaforma iCodium',
        type: 'pdf',
        size: '2.4 MB',
        dateAdded: '2023-05-15',
        chunks: 42,
        tokens: 18750
      },
      {
        id: 2,
        name: 'FAQ supporto clienti',
        description: 'Domande frequenti e risposte per il supporto clienti',
        type: 'docx',
        size: '1.1 MB',
        dateAdded: '2023-06-02',
        chunks: 28,
        tokens: 12340
      },
      {
        id: 3,
        name: 'Blog iCodium - Intelligenza Artificiale',
        description: 'Articoli del blog relativi all\'intelligenza artificiale e machine learning',
        type: 'webpage',
        size: '0.8 MB',
        dateAdded: '2023-06-10',
        chunks: 15,
        tokens: 8200
      },
      {
        id: 4,
        name: 'Politiche di sicurezza',
        description: 'Documentazione sulle politiche di sicurezza e privacy',
        type: 'text',
        size: '0.3 MB',
        dateAdded: '2023-05-28',
        chunks: 8,
        tokens: 4500
      }
    ];

    setSources(demoSources);
    setFilteredSources(demoSources);
  }, []);

  // Filter sources based on search term and type
  useEffect(() => {
    let filtered = sources;

    if (searchTerm) {
      filtered = filtered.filter(source =>
        source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        source.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(source => source.type === filterType);
    }

    setFilteredSources(filtered);
  }, [searchTerm, filterType, sources]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle filter type change
  const handleFilterChange = (e) => {
    setFilterType(e.target.value);
  };

  // Handle source click
  const handleSourceClick = (source) => {
    setSelectedSource(source);
    setShowSourceDetail(true);
  };

  // Handle source delete
  const handleSourceDelete = (e, sourceId) => {
    e.stopPropagation();

    // Chiedi conferma prima di eliminare
    if (confirm('Sei sicuro di voler eliminare questa fonte di conoscenza?')) {
      // Simulate API call to delete source
      const updatedSources = sources.filter(source => source.id !== sourceId);
      setSources(updatedSources);

      // Aggiorna anche i filteredSources
      setFilteredSources(prevFiltered => prevFiltered.filter(source => source.id !== sourceId));
    }
  };

  // Handle source edit
  const handleSourceEdit = (e, source) => {
    e.stopPropagation();
    // In a real app, this would open an edit form
    // For now, we'll show an alert to demonstrate the functionality
    alert(`Modifica fonte: ${source.name}\nIn un'applicazione reale, qui si aprirebbe un form di modifica.`);
  };

  // Get source icon based on type
  const getSourceIcon = (type) => {
    switch (type) {
      case 'pdf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'docx':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'webpage':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
        );
      case 'text':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="17" y1="10" x2="3" y2="10"></line>
            <line x1="21" y1="6" x2="3" y2="6"></line>
            <line x1="21" y1="14" x2="3" y2="14"></line>
            <line x1="17" y1="18" x2="3" y2="18"></line>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
            <polyline points="13 2 13 9 20 9"></polyline>
          </svg>
        );
    }
  };

  return (
    <div className="knowledge-sources-container">
      <div className="knowledge-sources-header">
        <h3 className="knowledge-sources-title">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
          </svg>
          Fonti di conoscenza
        </h3>
        <div className="knowledge-sources-actions">
          <button className="btn-primary" onClick={onAddKnowledge}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Aggiungi conoscenza
          </button>
        </div>
      </div>

      <div className="knowledge-sources-filter">
        <div className="filter-input">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <input
            type="text"
            placeholder="Cerca fonti di conoscenza..."
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <select
          className="filter-select"
          value={filterType}
          onChange={handleFilterChange}
        >
          <option value="all">Tutti i tipi</option>
          <option value="pdf">PDF</option>
          <option value="docx">DOCX</option>
          <option value="webpage">Pagina Web</option>
          <option value="text">Testo</option>
        </select>
      </div>

      {filteredSources.length > 0 ? (
        <div className="knowledge-sources">
          {filteredSources.map(source => (
            <div
              key={source.id}
              className="knowledge-source-item"
              onClick={() => handleSourceClick(source)}
            >
              <div className="knowledge-source-icon">
                {getSourceIcon(source.type)}
              </div>
              <div className="knowledge-source-info">
                <div className="knowledge-source-name">{source.name}</div>
                <div className="knowledge-source-description">{source.description}</div>
                <div className="knowledge-source-meta">
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    {source.dateAdded}
                  </div>
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                    </svg>
                    {source.size}
                  </div>
                  <div className="meta-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="17" y1="10" x2="3" y2="10"></line>
                      <line x1="21" y1="6" x2="3" y2="6"></line>
                      <line x1="21" y1="14" x2="3" y2="14"></line>
                      <line x1="17" y1="18" x2="3" y2="18"></line>
                    </svg>
                    {source.chunks} chunks
                  </div>
                </div>
              </div>
              <div className="knowledge-source-type">
                {source.type.toUpperCase()}
              </div>
              <div className="knowledge-source-actions">
                <button
                  className="source-action-btn view-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSourceClick(source);
                  }}
                  title="Visualizza dettagli"
                >
                  <i className="fa fa-eye"></i>
                </button>
                <button
                  className="source-action-btn edit-btn"
                  onClick={(e) => handleSourceEdit(e, source)}
                  title="Modifica fonte"
                >
                  <i className="fa fa-pencil"></i>
                </button>
                <button
                  className="source-action-btn delete-btn"
                  onClick={(e) => handleSourceDelete(e, source.id)}
                  title="Elimina fonte"
                >
                  <i className="fa fa-trash"></i>
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="empty-state">
          <div className="empty-state-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
          </div>
          <h3 className="empty-state-title">Nessuna fonte di conoscenza trovata</h3>
          <p className="empty-state-description">
            Aggiungi documenti, pagine web o testo per migliorare la conoscenza dell'agente.
          </p>
          <button className="btn-primary" onClick={onAddKnowledge}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Aggiungi conoscenza
          </button>
        </div>
      )}

      {showSourceDetail && selectedSource && (
        <div className="source-detail-modal" onClick={() => setShowSourceDetail(false)}>
          <div className="source-detail-content" onClick={(e) => e.stopPropagation()}>
            <div className="source-detail-header">
              <h3 className="source-detail-title">Dettagli fonte di conoscenza</h3>
              <button
                className="source-detail-close"
                onClick={() => setShowSourceDetail(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            <div className="source-detail-body">
              <div className="source-detail-info">
                <div className="source-detail-icon">
                  {getSourceIcon(selectedSource.type)}
                </div>
                <div className="source-detail-meta">
                  <h4 className="source-detail-name">{selectedSource.name}</h4>
                  <p className="source-detail-description">{selectedSource.description}</p>
                </div>
              </div>

              <div className="source-detail-stats">
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  Aggiunto il: {selectedSource.dateAdded}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  </svg>
                  Dimensione: {selectedSource.size}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="17" y1="10" x2="3" y2="10"></line>
                    <line x1="21" y1="6" x2="3" y2="6"></line>
                    <line x1="21" y1="14" x2="3" y2="14"></line>
                    <line x1="17" y1="18" x2="3" y2="18"></line>
                  </svg>
                  Chunks: {selectedSource.chunks}
                </div>
                <div className="source-stat">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path>
                    <line x1="16" y1="8" x2="2" y2="22"></line>
                    <line x1="17.5" y1="15" x2="9" y2="15"></line>
                  </svg>
                  Tokens: {selectedSource.tokens}
                </div>
              </div>

              <div className="source-detail-preview">
                <h4 className="source-detail-preview-title">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  Anteprima contenuto
                </h4>
                <div className="source-detail-preview-content">
                  {selectedSource.type === 'text' ?
                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl...' :
                    'Anteprima non disponibile per questo tipo di documento. Il contenuto è stato elaborato e suddiviso in chunks per l\'utilizzo da parte dell\'agente AI.'}
                </div>
              </div>
            </div>
            <div className="source-detail-actions">
              <button
                className="btn-secondary"
                onClick={() => setShowSourceDetail(false)}
              >
                Chiudi
              </button>
              <button
                className="btn-primary"
                onClick={(e) => handleSourceEdit(e, selectedSource)}
              >
                <i className="fa fa-pencil"></i>
                Modifica
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KnowledgeSources;
