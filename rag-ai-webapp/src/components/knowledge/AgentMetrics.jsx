import React, { useState, useEffect } from 'react';
import '../../styles/knowledge.css';

const AgentMetrics = ({ agent }) => {
  const [timeRange, setTimeRange] = useState('week');
  const [metrics, setMetrics] = useState(null);

  // Generate sample metrics data
  useEffect(() => {
    // Generate random data based on time range
    const generateData = () => {
      const now = new Date();
      const data = [];

      let days;
      switch(timeRange) {
        case 'day':
          days = 1;
          break;
        case 'week':
          days = 7;
          break;
        case 'month':
          days = 30;
          break;
        default:
          days = 7;
      }

      // Generate hourly data points
      const hoursToGenerate = days * 24;
      const hourlyInterval = Math.max(1, Math.floor(hoursToGenerate / 24)); // Limit to 24 data points

      for (let i = 0; i < hoursToGenerate; i += hourlyInterval) {
        const date = new Date(now);
        date.setHours(date.getHours() - i);

        data.push({
          timestamp: date.toISOString(),
          queries: Math.floor(Math.random() * 10) + 1,
          responseTime: Math.random() * 2 + 0.5, // 0.5-2.5 seconds
          satisfaction: Math.random() * 30 + 70, // 70-100%
          tokensUsed: Math.floor(Math.random() * 1000) + 200 // 200-1200 tokens
        });
      }

      // Sort by timestamp ascending
      data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

      // Calculate aggregated metrics
      const totalQueries = data.reduce((sum, point) => sum + point.queries, 0);
      const avgResponseTime = data.reduce((sum, point) => sum + point.responseTime, 0) / data.length;
      const avgSatisfaction = data.reduce((sum, point) => sum + point.satisfaction, 0) / data.length;
      const totalTokens = data.reduce((sum, point) => sum + point.tokensUsed, 0);

      // Calculate knowledge usage
      const knowledgeUsage = [];
      if (agent.sources && agent.sources.length > 0) {
        agent.sources.forEach(source => {
          knowledgeUsage.push({
            id: source.id,
            name: source.name,
            usageCount: Math.floor(Math.random() * totalQueries * 0.8), // Up to 80% of queries use this source
            relevanceScore: Math.floor(Math.random() * 30) + 70 // 70-100%
          });
        });

        // Sort by usage count descending
        knowledgeUsage.sort((a, b) => b.usageCount - a.usageCount);
      }

      return {
        timeSeriesData: data,
        aggregated: {
          totalQueries,
          avgResponseTime,
          avgSatisfaction,
          totalTokens
        },
        knowledgeUsage
      };
    };

    setMetrics(generateData());
  }, [timeRange, agent]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  };

  // Format short date for chart labels
  const formatShortDate = (dateString) => {
    const date = new Date(dateString);
    return timeRange === 'day'
      ? date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      : date.toLocaleDateString([], {month: 'short', day: 'numeric'});
  };

  if (!metrics) {
    return <div className="loading-metrics">Caricamento metriche...</div>;
  }

  // Prepare chart data
  const chartData = metrics.timeSeriesData.map(point => ({
    label: formatShortDate(point.timestamp),
    queries: point.queries,
    responseTime: point.responseTime,
    satisfaction: point.satisfaction,
    tokensUsed: point.tokensUsed
  }));

  // Get max values for scaling
  const maxQueries = Math.max(...chartData.map(d => d.queries));
  const maxResponseTime = Math.max(...chartData.map(d => d.responseTime));

  return (
    <div className="agent-metrics-container">
      <div className="metrics-header">
        <h3>Metriche e Performance</h3>
        <div className="time-range-selector">
          <button
            className={`time-range-btn ${timeRange === 'day' ? 'active' : ''}`}
            onClick={() => setTimeRange('day')}
          >
            Giorno
          </button>
          <button
            className={`time-range-btn ${timeRange === 'week' ? 'active' : ''}`}
            onClick={() => setTimeRange('week')}
          >
            Settimana
          </button>
          <button
            className={`time-range-btn ${timeRange === 'month' ? 'active' : ''}`}
            onClick={() => setTimeRange('month')}
          >
            Mese
          </button>
        </div>
      </div>

      <div className="metrics-summary">
        <div className="metric-card">
          <div className="metric-value">{metrics.aggregated.totalQueries}</div>
          <div className="metric-label">Query totali</div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{metrics.aggregated.avgResponseTime.toFixed(2)}s</div>
          <div className="metric-label">Tempo di risposta medio</div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{metrics.aggregated.avgSatisfaction.toFixed(1)}%</div>
          <div className="metric-label">Soddisfazione utenti</div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{metrics.aggregated.totalTokens.toLocaleString()}</div>
          <div className="metric-label">Token utilizzati</div>
        </div>
      </div>

      <div className="metrics-charts">
        <div className="chart-container">
          <h4>Attività nel tempo</h4>
          <div className="chart">
            <div className="chart-bars">
              {chartData.map((point, index) => (
                <div key={index} className="chart-bar-group">
                  <div
                    className="chart-bar queries"
                    style={{height: `${(point.queries / maxQueries) * 100}%`}}
                    title={`${point.queries} query`}
                  ></div>
                </div>
              ))}
            </div>
            <div className="chart-labels">
              {chartData.map((point, index) => (
                <div key={index} className="chart-label">
                  {index % Math.max(1, Math.floor(chartData.length / 6)) === 0 ? point.label : ''}
                </div>
              ))}
            </div>
          </div>
          <div className="chart-legend">
            <div className="legend-item">
              <span className="legend-color queries"></span>
              <span>Numero di query</span>
            </div>
          </div>
        </div>

        <div className="chart-container">
          <h4>Tempo di risposta</h4>
          <div className="chart">
            <div className="chart-bars">
              {chartData.map((point, index) => (
                <div key={index} className="chart-bar-group">
                  <div
                    className="chart-bar response-time"
                    style={{height: `${(point.responseTime / maxResponseTime) * 100}%`}}
                    title={`${point.responseTime.toFixed(2)}s`}
                  ></div>
                </div>
              ))}
            </div>
            <div className="chart-labels">
              {chartData.map((point, index) => (
                <div key={index} className="chart-label">
                  {index % Math.max(1, Math.floor(chartData.length / 6)) === 0 ? point.label : ''}
                </div>
              ))}
            </div>
          </div>
          <div className="chart-legend">
            <div className="legend-item">
              <span className="legend-color response-time"></span>
              <span>Tempo di risposta (secondi)</span>
            </div>
          </div>
        </div>
      </div>

      <div className="knowledge-usage">
        <h4>Utilizzo Knowledge Base</h4>
        {metrics.knowledgeUsage.length > 0 ? (
          <div className="knowledge-table">
            <div className="knowledge-table-header">
              <div className="knowledge-name">Fonte</div>
              <div className="knowledge-usage">Utilizzo</div>
              <div className="knowledge-relevance">Rilevanza</div>
            </div>
            {metrics.knowledgeUsage.map(source => (
              <div key={source.id} className="knowledge-table-row">
                <div className="knowledge-name">{source.name}</div>
                <div className="knowledge-usage">
                  <div className="usage-bar-container">
                    <div
                      className="usage-bar"
                      style={{width: `${(source.usageCount / metrics.aggregated.totalQueries) * 100}%`}}
                    ></div>
                  </div>
                  <span>{source.usageCount} query</span>
                </div>
                <div className="knowledge-relevance">
                  <div className="relevance-indicator" style={{
                    backgroundColor: source.relevanceScore > 90
                      ? '#4CAF50'
                      : source.relevanceScore > 80
                        ? '#FFC107'
                        : '#FF5722'
                  }}></div>
                  <span>{source.relevanceScore}%</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-knowledge">
            Nessuna fonte di conoscenza utilizzata in questo periodo.
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentMetrics;
