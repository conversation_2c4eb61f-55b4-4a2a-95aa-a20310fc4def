import React, { useState } from 'react';
import '../../styles/agent-config.css';

const AgentConfig = ({ agent, onSaveConfig }) => {
  const [config, setConfig] = useState({
    temperature: agent.config?.temperature || 0.7,
    maxTokens: agent.config?.maxTokens || 2048,
    topP: agent.config?.topP || 0.9,
    frequencyPenalty: agent.config?.frequencyPenalty || 0.0,
    presencePenalty: agent.config?.presencePenalty || 0.0,
    responseFormat: agent.config?.responseFormat || 'auto',
    contextWindow: agent.config?.contextWindow || 'medium',
    knowledgeWeight: agent.config?.knowledgeWeight || 0.8,
    systemPrompt: agent.config?.systemPrompt || `Sei un assistente AI chiamato ${agent.name}. Il tuo compito è fornire risposte accurate e utili basate sulla knowledge base a tua disposizione.`
  });

  const [activeSection, setActiveSection] = useState('basic');

  const handleChange = (e) => {
    const { name, value, type } = e.target;

    // Convert numeric values
    const processedValue = type === 'number' || type === 'range'
      ? parseFloat(value)
      : value;

    setConfig({
      ...config,
      [name]: processedValue
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSaveConfig(config);
  };

  return (
    <div className="agent-config-container">
      <h3>Configurazione Avanzata</h3>
      <p className="config-description">
        Personalizza il comportamento e le prestazioni dell'agente modificando i parametri di configurazione. Queste impostazioni influenzano il modo in cui l'agente genera risposte e utilizza la knowledge base.
      </p>

      <div className="config-tabs">
        <button
          className={`config-tab ${activeSection === 'basic' ? 'active' : ''}`}
          onClick={() => setActiveSection('basic')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
          Parametri Base
        </button>
        <button
          className={`config-tab ${activeSection === 'advanced' ? 'active' : ''}`}
          onClick={() => setActiveSection('advanced')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 20h9"></path>
            <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
          </svg>
          Parametri Avanzati
        </button>
        <button
          className={`config-tab ${activeSection === 'prompt' ? 'active' : ''}`}
          onClick={() => setActiveSection('prompt')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
          System Prompt
        </button>
      </div>

      <form onSubmit={handleSubmit} className="config-form">
        {activeSection === 'basic' && (
          <div className="config-section">
            <div className="config-group">
              <div className="parameter-info">
                i
                <div className="parameter-tooltip">
                  La temperatura controlla la casualità delle risposte. Valori più bassi (0.1-0.4) producono risposte più deterministiche, valori più alti (0.7-1.0) producono risposte più creative.
                </div>
              </div>
              <label>
                Temperatura
                <span className="config-value">{config.temperature.toFixed(1)}</span>
              </label>
              <input
                type="range"
                name="temperature"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature}
                onChange={handleChange}
                className="slider"
              />
              <div className="slider-labels">
                <span>Preciso</span>
                <span>Creativo</span>
              </div>
              <div className="config-description">
                Controlla la casualità delle risposte. Valori più bassi producono risposte più deterministiche e focalizzate, valori più alti producono risposte più creative e varie.
              </div>
            </div>

            <div className="config-group">
              <div className="parameter-info">
                i
                <div className="parameter-tooltip">
                  Determina la lunghezza massima della risposta generata dall'agente. Un valore più alto consente risposte più lunghe ma aumenta il consumo di token.
                </div>
              </div>
              <label>
                Lunghezza massima risposta
                <span className="config-value">{config.maxTokens}</span>
              </label>
              <select
                name="maxTokens"
                value={config.maxTokens}
                onChange={handleChange}
                className="config-select"
              >
                <option value="512">Breve (512 tokens)</option>
                <option value="1024">Medio (1024 tokens)</option>
                <option value="2048">Lungo (2048 tokens)</option>
                <option value="4096">Molto lungo (4096 tokens)</option>
              </select>
              <div className="config-description">
                Il numero massimo di token che l'agente può generare in una risposta.
              </div>
            </div>

            <div className="config-group">
              <div className="parameter-info">
                i
                <div className="parameter-tooltip">
                  Controlla quanto l'agente si basa sulla knowledge base rispetto alla sua conoscenza generale. Valori più alti danno maggiore priorità alle informazioni nella knowledge base.
                </div>
              </div>
              <label>
                Peso della knowledge base
                <span className="config-value">{config.knowledgeWeight.toFixed(1)}</span>
              </label>
              <input
                type="range"
                name="knowledgeWeight"
                min="0"
                max="1"
                step="0.1"
                value={config.knowledgeWeight}
                onChange={handleChange}
                className="slider"
              />
              <div className="slider-labels">
                <span>Conoscenza generale</span>
                <span>Knowledge base</span>
              </div>
              <div className="config-description">
                Determina quanto l'agente si basa sulla knowledge base rispetto alla sua conoscenza generale.
              </div>
            </div>
          </div>
        )}

        {activeSection === 'advanced' && (
          <div className="config-section">
            <div className="config-group">
              <label>
                Top P
                <span className="config-value">{config.topP.toFixed(2)}</span>
              </label>
              <input
                type="range"
                name="topP"
                min="0.1"
                max="1"
                step="0.05"
                value={config.topP}
                onChange={handleChange}
                className="slider"
              />
              <div className="config-description">
                Controlla la diversità considerando solo le parole più probabili. 1.0 significa considerare tutte le possibilità.
              </div>
            </div>

            <div className="config-group">
              <label>
                Penalità di frequenza
                <span className="config-value">{config.frequencyPenalty.toFixed(1)}</span>
              </label>
              <input
                type="range"
                name="frequencyPenalty"
                min="0"
                max="2"
                step="0.1"
                value={config.frequencyPenalty}
                onChange={handleChange}
                className="slider"
              />
              <div className="config-description">
                Riduce la probabilità di ripetere le stesse parole. Valori più alti penalizzano maggiormente le ripetizioni.
              </div>
            </div>

            <div className="config-group">
              <label>
                Penalità di presenza
                <span className="config-value">{config.presencePenalty.toFixed(1)}</span>
              </label>
              <input
                type="range"
                name="presencePenalty"
                min="0"
                max="2"
                step="0.1"
                value={config.presencePenalty}
                onChange={handleChange}
                className="slider"
              />
              <div className="config-description">
                Aumenta la probabilità di introdurre nuovi argomenti. Valori più alti incoraggiano l'agente a parlare di nuovi concetti.
              </div>
            </div>

            <div className="config-group">
              <label>Formato risposta</label>
              <select
                name="responseFormat"
                value={config.responseFormat}
                onChange={handleChange}
                className="config-select"
              >
                <option value="auto">Automatico</option>
                <option value="text">Solo testo</option>
                <option value="json">JSON</option>
                <option value="markdown">Markdown</option>
              </select>
              <div className="config-description">
                Specifica il formato in cui l'agente dovrebbe fornire le risposte.
              </div>
            </div>

            <div className="config-group">
              <label>Finestra di contesto</label>
              <select
                name="contextWindow"
                value={config.contextWindow}
                onChange={handleChange}
                className="config-select"
              >
                <option value="small">Piccola (ultimi 3 messaggi)</option>
                <option value="medium">Media (ultimi 10 messaggi)</option>
                <option value="large">Grande (ultimi 20 messaggi)</option>
                <option value="full">Completa (intera conversazione)</option>
              </select>
              <div className="config-description">
                Determina quanti messaggi precedenti vengono considerati per il contesto della conversazione.
              </div>
            </div>
          </div>
        )}

        {activeSection === 'prompt' && (
          <div className="config-section">
            <div className="config-group full-width">
              <label>System Prompt</label>
              <textarea
                name="systemPrompt"
                value={config.systemPrompt}
                onChange={handleChange}
                rows="10"
                className="config-textarea"
                placeholder="Inserisci il system prompt per l'agente..."
              />
              <div className="config-description">
                Il system prompt definisce il comportamento, le capacità e i limiti dell'agente. Usa variabili come {'{knowledge_sources}'} per riferimenti dinamici.
              </div>

              <div className="prompt-templates">
                <h4>Template disponibili</h4>
                <div className="template-buttons">
                  <button
                    type="button"
                    onClick={() => setConfig({
                      ...config,
                      systemPrompt: `Sei un assistente AI esperto chiamato ${agent.name}. Il tuo compito è fornire risposte accurate e utili basate sulla knowledge base a tua disposizione. Cita sempre le fonti quando possibile.`
                    })}
                  >
                    Assistente standard
                  </button>
                  <button
                    type="button"
                    onClick={() => setConfig({
                      ...config,
                      systemPrompt: `Sei un esperto di analisi dati chiamato ${agent.name}. Analizza i dati forniti e presenta insights chiari e actionable. Usa un linguaggio preciso e tecnico.`
                    })}
                  >
                    Analista dati
                  </button>
                  <button
                    type="button"
                    onClick={() => setConfig({
                      ...config,
                      systemPrompt: `Sei un assistente di supporto tecnico chiamato ${agent.name}. Aiuta gli utenti a risolvere problemi tecnici in modo chiaro e paziente. Fornisci istruzioni passo-passo e verifica la comprensione.`
                    })}
                  >
                    Supporto tecnico
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="config-actions">
          <button type="submit" className="btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17 21 17 13 7 13 7 21"></polyline>
              <polyline points="7 3 7 8 15 8"></polyline>
            </svg>
            Salva configurazione
          </button>
          <button type="button" className="btn-secondary" onClick={() => setConfig({
            temperature: agent.config?.temperature || 0.7,
            maxTokens: agent.config?.maxTokens || 2048,
            topP: agent.config?.topP || 0.9,
            frequencyPenalty: agent.config?.frequencyPenalty || 0.0,
            presencePenalty: agent.config?.presencePenalty || 0.0,
            responseFormat: agent.config?.responseFormat || 'auto',
            contextWindow: agent.config?.contextWindow || 'medium',
            knowledgeWeight: agent.config?.knowledgeWeight || 0.8,
            systemPrompt: agent.config?.systemPrompt || `Sei un assistente AI chiamato ${agent.name}. Il tuo compito è fornire risposte accurate e utili basate sulla knowledge base a tua disposizione.`
          })}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
              <path d="M3 3v5h5"></path>
            </svg>
            Ripristina
          </button>
        </div>
      </form>
    </div>
  );
};

export default AgentConfig;
