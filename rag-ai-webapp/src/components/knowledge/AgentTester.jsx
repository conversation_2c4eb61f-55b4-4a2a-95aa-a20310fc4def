import React, { useState, useRef, useEffect } from 'react';
import '../../styles/agent-tester.css';

const AgentTester = ({ agent }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      content: `Ciao! Sono ${agent.name}. Come posso aiutarti oggi?`,
      isAi: true,
      timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
      sources: []
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [debugInfo, setDebugInfo] = useState({
    tokens: 0,
    processingTime: 0,
    sourcesUsed: [],
    confidenceScore: 0,
    reasoning: [],
    llmCalls: 0,
    promptTokens: 0,
    completionTokens: 0
  });

  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      content: inputMessage,
      isAi: false,
      timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsProcessing(true);

    // Simulate AI processing
    const startTime = performance.now();

    setTimeout(() => {
      // Generate random sources from agent's knowledge base
      const randomSources = [];
      if (agent.sources && agent.sources.length > 0) {
        const numSources = Math.floor(Math.random() * 3) + 1; // 1-3 sources
        for (let i = 0; i < numSources && i < agent.sources.length; i++) {
          const randomIndex = Math.floor(Math.random() * agent.sources.length);
          const source = agent.sources[randomIndex];
          if (!randomSources.find(s => s.id === source.id)) {
            randomSources.push({
              ...source,
              relevance: Math.floor(Math.random() * 30) + 70 // 70-99% relevance
            });
          }
        }
      }

      // Generate random reasoning steps
      const reasoningSteps = [
        "Analisi della query dell'utente",
        "Ricerca nella knowledge base",
        `Trovati ${randomSources.length} documenti rilevanti`,
        "Generazione della risposta basata sulle fonti"
      ];

      // Simulate AI response
      let aiResponse;

      if (inputMessage.toLowerCase().includes('aiuto') || inputMessage.toLowerCase().includes('help')) {
        aiResponse = {
          id: messages.length + 2,
          content: `Posso aiutarti con informazioni su ${agent.description}. Puoi farmi domande specifiche e utilizzerò la mia knowledge base per fornirti risposte accurate.`,
          isAi: true,
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
          sources: []
        };
      } else if (inputMessage.toLowerCase().includes('conoscenza') || inputMessage.toLowerCase().includes('knowledge')) {
        aiResponse = {
          id: messages.length + 2,
          content: `La mia knowledge base contiene ${agent.sources ? agent.sources.length : 0} fonti di informazione, tra cui documenti, manuali e guide. Posso accedere a queste informazioni per rispondere alle tue domande in modo accurato.`,
          isAi: true,
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
          sources: randomSources
        };
      } else {
        aiResponse = {
          id: messages.length + 2,
          content: `Basandomi sulla tua richiesta "${inputMessage}", ho analizzato le informazioni disponibili nella mia knowledge base. ${randomSources.length > 0 ? 'Ecco cosa ho trovato:' : ''}\n\n${randomSources.length > 0 ? 'Le fonti indicano che questo argomento è importante nel contesto di ' + agent.description + '. Ci sono diversi aspetti da considerare...' : 'Non ho trovato informazioni specifiche su questo argomento nella mia knowledge base, ma posso comunque aiutarti con le mie conoscenze generali.'}\n\nPosso fornirti ulteriori dettagli se necessario.`,
          isAi: true,
          timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
          sources: randomSources
        };
      }

      setMessages(prev => [...prev, aiResponse]);
      setIsProcessing(false);

      // Update debug info
      const endTime = performance.now();
      const totalTokens = Math.floor(Math.random() * 500) + 100; // 100-600 tokens
      const promptTokens = Math.floor(totalTokens * 0.7);
      const completionTokens = totalTokens - promptTokens;

      setDebugInfo({
        tokens: totalTokens,
        processingTime: ((endTime - startTime) / 1000).toFixed(2), // in seconds
        sourcesUsed: randomSources,
        confidenceScore: Math.floor(Math.random() * 20) + 80, // 80-99% confidence
        reasoning: reasoningSteps,
        llmCalls: Math.floor(Math.random() * 2) + 1, // 1-2 LLM calls
        promptTokens: promptTokens,
        completionTokens: completionTokens
      });
    }, 1500); // Simulate processing delay
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="agent-tester-container">
      <div className="tester-header">
        <h3>Test dell'Agente</h3>
        <div className="tester-controls">
          <button
            className={`debug-toggle ${showDebug ? 'active' : ''}`}
            onClick={() => setShowDebug(!showDebug)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
              <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
              <line x1="6" y1="6" x2="6.01" y2="6"></line>
              <line x1="6" y1="18" x2="6.01" y2="18"></line>
            </svg>
            {showDebug ? 'Nascondi Debug' : 'Mostra Debug'}
          </button>
        </div>
      </div>

      <div className="tester-content">
        <div className="tester-chat">
          <div className="chat-messages">
            {messages.map(message => (
              <div
                key={message.id}
                className={`chat-message ${message.isAi ? 'ai' : 'user'}`}
              >
                <div className="message-header">
                  <span className="message-sender">
                    {message.isAi ? agent.name : 'Tu'}
                  </span>
                  <span className="message-time">{message.timestamp}</span>
                </div>
                <div className="message-content">
                  {message.content}
                </div>
                {message.isAi && message.sources && message.sources.length > 0 && (
                  <div className="message-sources">
                    <div className="sources-header">Fonti utilizzate:</div>
                    <ul className="sources-list">
                      {message.sources.map(source => (
                        <li key={source.id} className="source-item">
                          <div className="source-name">{source.name}</div>
                          <div className="source-relevance">
                            <div
                              className="relevance-bar"
                              style={{width: `${source.relevance}%`}}
                            ></div>
                            <span>{source.relevance}%</span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          <div className="chat-input-container">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Scrivi un messaggio..."
              disabled={isProcessing}
              className="chat-input"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isProcessing}
              className="send-button"
            >
              {isProcessing ? (
                <div className="loading-spinner"></div>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              )}
            </button>
          </div>
        </div>

        {showDebug && (
          <div className="tester-debug">
            <h4>Informazioni di Debug</h4>

            <div className="debug-section">
              <h5>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 20v-6M6 20V10M18 20V4"></path>
                </svg>
                Statistiche
              </h5>
              <div className="debug-stats">
                <div className="stat-item">
                  <span className="stat-label">Token totali:</span>
                  <span className="stat-value">{debugInfo.tokens}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Token prompt:</span>
                  <span className="stat-value">{debugInfo.promptTokens}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Token completamento:</span>
                  <span className="stat-value">{debugInfo.completionTokens}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Tempo di elaborazione:</span>
                  <span className="stat-value">{debugInfo.processingTime}s</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Chiamate LLM:</span>
                  <span className="stat-value">{debugInfo.llmCalls}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Confidenza:</span>
                  <span className="stat-value">{debugInfo.confidenceScore}%</span>
                </div>
              </div>
            </div>

            <div className="debug-section">
              <h5>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path>
                  <line x1="2" y1="20" x2="2" y2="20"></line>
                </svg>
                Processo di ragionamento
              </h5>
              <ol className="reasoning-steps">
                {debugInfo.reasoning.map((step, index) => (
                  <li key={index} className="reasoning-step">
                    <div className="reasoning-step-number">{index + 1}</div>
                    <div className="reasoning-step-content">{step}</div>
                  </li>
                ))}
              </ol>
            </div>

            <div className="debug-section">
              <h5>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
                Configurazione
              </h5>
              <div className="debug-config">
                <div className="config-item">
                  <span className="config-label">Modello:</span>
                  <span className="config-value">{agent.model || 'icodium-agata-1'}</span>
                </div>
                <div className="config-item">
                  <span className="config-label">Temperatura:</span>
                  <span className="config-value">{agent.config?.temperature || 0.7}</span>
                </div>
                <div className="config-item">
                  <span className="config-label">Max tokens:</span>
                  <span className="config-value">{agent.config?.maxTokens || 2048}</span>
                </div>
                <div className="config-item">
                  <span className="config-label">Top P:</span>
                  <span className="config-value">{agent.config?.topP || 1.0}</span>
                </div>
                <div className="config-item">
                  <span className="config-label">Frequency penalty:</span>
                  <span className="config-value">{agent.config?.frequencyPenalty || 0.0}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentTester;
