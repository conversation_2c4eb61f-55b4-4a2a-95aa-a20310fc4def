import React, { useEffect, useRef, useState } from 'react';
import '../../styles/agent-detail.css';

const AgentGraph = ({ agents, onSelectAgent }) => {
  const canvasRef = useRef(null);
  const [hoveredAgent, setHoveredAgent] = useState(null);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
  const [agentNodes, setAgentNodes] = useState([]);
  const [connections, setConnections] = useState([]);

  // Calculate positions for agents in a force-directed layout
  useEffect(() => {
    if (!agents || agents.length === 0) return;

    // Create nodes for main agents
    const mainNodes = agents.map((agent, index) => {
      const angle = (2 * Math.PI * index) / agents.length;
      const radius = Math.min(canvasSize.width, canvasSize.height) * 0.35;

      return {
        id: agent.id,
        agent,
        x: canvasSize.width / 2 + radius * Math.cos(angle),
        y: canvasSize.height / 2 + radius * Math.sin(angle),
        radius: 40,
        type: 'main'
      };
    });

    // Create nodes for sub-agents
    const subNodes = [];
    const connectionLines = [];

    agents.forEach((agent, agentIndex) => {
      if (agent.subAgents && agent.subAgents.length > 0) {
        const parentNode = mainNodes[agentIndex];

        agent.subAgents.forEach((subAgent, subIndex) => {
          const subAngle = (2 * Math.PI * subIndex) / agent.subAgents.length;
          const subRadius = 100;

          const subNode = {
            id: subAgent.id,
            agent: subAgent,
            x: parentNode.x + subRadius * Math.cos(subAngle),
            y: parentNode.y + subRadius * Math.sin(subAngle),
            radius: 30,
            type: 'sub',
            parentId: agent.id
          };

          subNodes.push(subNode);

          // Add connection between parent and sub-agent
          connectionLines.push({
            from: agent.id,
            to: subAgent.id,
            type: 'parent-child'
          });
        });
      }
    });

    // Add connections based on knowledge sharing or other relationships
    agents.forEach(agent => {
      if (agent.connections) {
        agent.connections.forEach(conn => {
          connectionLines.push({
            from: agent.id,
            to: conn.agentId,
            type: conn.type
          });
        });
      }
    });

    setAgentNodes([...mainNodes, ...subNodes]);
    setConnections(connectionLines);
  }, [agents, canvasSize]);

  // Initialize canvas and handle resize
  useEffect(() => {
    const updateCanvasSize = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const container = canvas.parentElement;
      setCanvasSize({
        width: container.clientWidth,
        height: container.clientHeight
      });
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    return () => {
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, []);

  // Draw the graph
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || agentNodes.length === 0) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvasSize.width, canvasSize.height);

    // Draw connections
    connections.forEach(conn => {
      const fromNode = agentNodes.find(node => node.id === conn.from);
      const toNode = agentNodes.find(node => node.id === conn.to);

      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);

        if (conn.type === 'parent-child') {
          ctx.strokeStyle = 'rgba(113, 34, 255, 0.6)';
          ctx.lineWidth = 2;
        } else {
          ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
          ctx.lineWidth = 1;
        }

        ctx.stroke();
      }
    });

    // Draw nodes
    agentNodes.forEach(node => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);

      if (node.id === hoveredAgent) {
        ctx.fillStyle = node.type === 'main' ? 'rgba(113, 34, 255, 0.9)' : 'rgba(113, 34, 255, 0.7)';
      } else {
        ctx.fillStyle = node.type === 'main' ? 'rgba(113, 34, 255, 0.7)' : 'rgba(113, 34, 255, 0.5)';
      }

      ctx.fill();

      // Draw agent name
      ctx.font = node.type === 'main' ? '14px Arial' : '12px Arial';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Truncate name if too long
      let displayName = node.agent.name;
      if (displayName.length > 12) {
        displayName = displayName.substring(0, 10) + '...';
      }

      ctx.fillText(displayName, node.x, node.y);

      // Draw status indicator
      ctx.beginPath();
      ctx.arc(node.x + node.radius - 5, node.y - node.radius + 5, 5, 0, 2 * Math.PI);
      ctx.fillStyle = node.agent.status === 'active' ? '#4CAF50' : '#F44336';
      ctx.fill();
    });
  }, [agentNodes, connections, canvasSize, hoveredAgent]);

  // Handle mouse interactions
  const handleMouseMove = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if mouse is over any agent node
    const hoveredNode = agentNodes.find(node => {
      const dx = x - node.x;
      const dy = y - node.y;
      return Math.sqrt(dx * dx + dy * dy) <= node.radius;
    });

    setHoveredAgent(hoveredNode ? hoveredNode.id : null);

    // Change cursor style
    canvas.style.cursor = hoveredNode ? 'pointer' : 'default';
  };

  const handleClick = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if click is on any agent node
    const clickedNode = agentNodes.find(node => {
      const dx = x - node.x;
      const dy = y - node.y;
      return Math.sqrt(dx * dx + dy * dy) <= node.radius;
    });

    if (clickedNode) {
      const agent = agents.find(a => a.id === clickedNode.id) ||
                    agents.flatMap(a => a.subAgents || []).find(sa => sa.id === clickedNode.id);

      if (agent) {
        onSelectAgent(agent);
      }
    }
  };

  return (
    <div className="agent-graph-container">
      <div className="agent-graph-header">
        <h3>Visualizzazione Agenti</h3>
        <div className="agent-graph-legend">
          <div className="legend-item">
            <span className="legend-color main-agent"></span>
            <span>Agente principale</span>
          </div>
          <div className="legend-item">
            <span className="legend-color sub-agent"></span>
            <span>Sotto-agente</span>
          </div>
          <div className="legend-item">
            <span className="legend-status active"></span>
            <span>Attivo</span>
          </div>
          <div className="legend-item">
            <span className="legend-status inactive"></span>
            <span>Inattivo</span>
          </div>
        </div>
      </div>

      <div className="agent-graph-canvas-container">
        <canvas
          ref={canvasRef}
          width={canvasSize.width}
          height={canvasSize.height}
          onMouseMove={handleMouseMove}
          onClick={handleClick}
        />

        {hoveredAgent && (
          <div className="agent-tooltip">
            {agentNodes.find(node => node.id === hoveredAgent)?.agent.name}
          </div>
        )}
      </div>

      <div className="agent-graph-info">
        <p>Clicca su un agente per visualizzare i dettagli e configurarlo.</p>
      </div>
    </div>
  );
};

export default AgentGraph;
