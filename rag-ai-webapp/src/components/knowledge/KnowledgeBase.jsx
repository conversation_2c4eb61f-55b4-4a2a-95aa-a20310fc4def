import { useState } from 'react';
import AgentList from './AgentList';
import AgentDeta<PERSON> from './AgentDetail';
import CreateAgent from './CreateAgent';
import AddKnowledge from './AddKnowledge';
import AgentGraph from './AgentGraph';
import '../../styles/agents-ai.css';

const KnowledgeBase = () => {
  // State management
  const [activeView, setActiveView] = useState('agents'); // agents, createAgent, agentDetail, addKnowledge
  const [viewMode, setViewMode] = useState('list'); // list, graph
  const [displayMode, setDisplayMode] = useState('list'); // list, grid
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [parentAgent, setParentAgent] = useState(null); // For creating sub-agents
  const [selectedSource, setSelectedSource] = useState(null);

  // Sample agents data with hierarchy
  const [agents, setAgents] = useState([
    {
      id: 'a1',
      name: 'Assistente Supporto',
      model: 'Agata 1',
      description: 'Gestisce le richieste dei clienti e i ticket di supporto',
      avatar: 'A',
      avatarColor: '#0e66b0',
      status: 'active',
      sources: [
        {
          id: 's1',
          name: 'Manuale di supporto tecnico',
          type: 'pdf',
          description: 'Documentazione completa sulle procedure di supporto',
          size: '2.4 MB',
          dateAdded: '2023-05-15',
        },
        {
          id: 's2',
          name: 'FAQ Clienti',
          type: 'docx',
          description: 'Domande frequenti dei clienti con risposte standard',
          size: '1.8 MB',
          dateAdded: '2023-06-02',
        }
      ],
      subAgents: [
        {
          id: 'a1-sub1',
          name: 'Analisi Ticket',
          model: 'Scorpion V2',
          description: 'Analizza e categorizza i ticket in arrivo',
          avatar: 'AT',
          avatarColor: '#009b8e',
          status: 'active',
          sources: [
            {
              id: 's3',
              name: 'Categorizzazione Ticket',
              type: 'pdf',
              description: 'Linee guida per la categorizzazione dei ticket',
              size: '1.2 MB',
              dateAdded: '2023-05-20',
            }
          ]
        },
        {
          id: 'a1-sub2',
          name: 'Risposte Automatiche',
          model: 'Maverick',
          description: 'Genera risposte automatiche per problemi comuni',
          avatar: 'RA',
          avatarColor: '#089a44',
          status: 'active',
          sources: [
            {
              id: 's4',
              name: 'Template Risposte',
              type: 'json',
              description: 'Template per risposte automatiche',
              size: '0.5 MB',
              dateAdded: '2023-05-25',
            }
          ]
        }
      ]
    },
    {
      id: 'a2',
      name: 'Analista Dati',
      model: 'Scorpion V2',
      description: 'Analizza i dati aziendali e genera report',
      avatar: 'D',
      avatarColor: '#009b8e',
      status: 'active',
      sources: [
        {
          id: 's5',
          name: 'Database Clienti',
          type: 'csv',
          description: 'Database completo dei clienti',
          size: '5.7 MB',
          dateAdded: '2023-04-10',
        }
      ],
      subAgents: [
        {
          id: 'a2-sub1',
          name: 'Visualizzazione Dati',
          model: 'Agata 1',
          description: 'Crea visualizzazioni e grafici dai dati analizzati',
          avatar: 'VD',
          avatarColor: '#0e66b0',
          status: 'active',
          sources: []
        }
      ]
    },
    {
      id: 'a3',
      name: 'Ricerca Documenti',
      model: 'Maverick',
      description: 'Cerca informazioni nei documenti aziendali',
      avatar: 'R',
      avatarColor: '#e74c3c',
      status: 'inactive',
      sources: [],
      subAgents: []
    }
  ]);

  // Handle agent selection
  const handleAgentSelect = (agent) => {
    setSelectedAgent(agent);
    setActiveView('agentDetail');
  };

  // Handle creating a new agent
  const handleCreateAgent = (parent = null) => {
    setParentAgent(parent);
    setActiveView('createAgent');
  };

  // Handle saving a new agent
  const handleSaveAgent = (newAgent) => {
    if (parentAgent) {
      // Creating a sub-agent
      const updatedAgents = agents.map(agent => {
        if (agent.id === parentAgent.id) {
          return {
            ...agent,
            subAgents: [
              ...agent.subAgents,
              {
                ...newAgent,
                id: `${agent.id}-sub${agent.subAgents.length + 1}`,
                sources: []
              }
            ]
          };
        }
        return agent;
      });
      setAgents(updatedAgents);
    } else {
      // Creating a main agent
      setAgents([...agents, {
        ...newAgent,
        id: `a${agents.length + 1}`,
        sources: [],
        subAgents: []
      }]);
    }
    setParentAgent(null);
    setActiveView('agents');
  };

  // Handle adding knowledge to an agent
  const handleAddKnowledge = (agent) => {
    setSelectedAgent(agent);
    setActiveView('addKnowledge');
  };

  // Handle saving knowledge source
  const handleSaveKnowledge = (source) => {
    if (selectedAgent) {
      const isSubAgent = selectedAgent.id.includes('-');
      const mainAgentId = isSubAgent ? selectedAgent.id.split('-')[0] : selectedAgent.id;

      const updatedAgents = agents.map(agent => {
        if (agent.id === mainAgentId) {
          if (isSubAgent) {
            // Adding to sub-agent
            return {
              ...agent,
              subAgents: agent.subAgents.map(subAgent => {
                if (subAgent.id === selectedAgent.id) {
                  return {
                    ...subAgent,
                    sources: [...subAgent.sources, {
                      ...source,
                      id: `s${subAgent.sources.length + 1}-${subAgent.id}`,
                      dateAdded: new Date().toISOString().split('T')[0]
                    }]
                  };
                }
                return subAgent;
              })
            };
          } else {
            // Adding to main agent
            return {
              ...agent,
              sources: [...agent.sources, {
                ...source,
                id: `s${agent.sources.length + 1}`,
                dateAdded: new Date().toISOString().split('T')[0]
              }]
            };
          }
        }
        return agent;
      });

      setAgents(updatedAgents);
    }
    setSelectedAgent(null);
    setActiveView('agents');
  };

  // Handle going back to the previous view
  const handleBack = () => {
    setSelectedAgent(null);
    setParentAgent(null);
    setSelectedSource(null);
    setActiveView('agents');
  };

  // Calculate total sources count
  const calculateTotalSources = () => {
    return agents.reduce((acc, agent) => {
      const mainAgentSources = agent.sources ? agent.sources.length : 0;
      const subAgentSources = agent.subAgents ? agent.subAgents.reduce((subAcc, subAgent) => {
        return subAcc + (subAgent.sources ? subAgent.sources.length : 0);
      }, 0) : 0;
      return acc + mainAgentSources + subAgentSources;
    }, 0);
  };

  // Render the appropriate view based on activeView state
  const renderView = () => {
    switch (activeView) {
      case 'agents':
        return (
          <div className="agents-ai-container">
            <div className="agents-ai-header">
              <div className="agents-ai-title-container">
                <h1 className="agents-ai-title">Agenti AI</h1>
                <p className="agents-ai-subtitle">
                  Gestisci e configura gli agenti AI per la tua knowledge base
                </p>
              </div>
              <div className="agents-ai-actions">
                <button
                  className="create-agent-button"
                  onClick={() => handleCreateAgent()}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Crea nuovo agente
                </button>
              </div>
            </div>

            <div className="agents-ai-tabs">
              <div className="tabs-container">
                <button
                  className={`tab-button ${viewMode === 'list' ? 'active' : ''}`}
                  onClick={() => setViewMode('list')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="8" y1="6" x2="21" y2="6"></line>
                    <line x1="8" y1="12" x2="21" y2="12"></line>
                    <line x1="8" y1="18" x2="21" y2="18"></line>
                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                  </svg>
                  Lista
                </button>
                <button
                  className={`tab-button ${viewMode === 'graph' ? 'active' : ''}`}
                  onClick={() => setViewMode('graph')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="18" cy="5" r="3"></circle>
                    <circle cx="6" cy="12" r="3"></circle>
                    <circle cx="18" cy="19" r="3"></circle>
                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                  </svg>
                  Grafo
                </button>
              </div>
              <div className="view-options">
                <button
                  className={`view-option-button ${displayMode === 'grid' ? 'active' : ''}`}
                  title="Visualizza come griglia"
                  onClick={() => setDisplayMode('grid')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                </button>
                <button
                  className={`view-option-button ${displayMode === 'list' ? 'active' : ''}`}
                  title="Visualizza come lista"
                  onClick={() => setDisplayMode('list')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </svg>
                </button>
              </div>
            </div>

            <div className="agents-ai-content">
              <div className="agents-ai-stats">
                <div className="stat-card">
                  <div className="stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <div className="stat-content">
                    <div className="stat-value">{agents.length}</div>
                    <div className="stat-label">Agenti totali</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <div className="stat-content">
                    <div className="stat-value">{agents.filter(a => a.status === 'active').length}</div>
                    <div className="stat-label">Agenti attivi</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </div>
                  <div className="stat-content">
                    <div className="stat-value">{calculateTotalSources()}</div>
                    <div className="stat-label">Fonti collegate</div>
                  </div>
                </div>
              </div>

              {viewMode === 'list' ? (
                <AgentList
                  agents={agents}
                  onSelectAgent={handleAgentSelect}
                  onCreateAgent={handleCreateAgent}
                  displayMode={displayMode}
                />
              ) : (
                <div className="agent-graph-container">
                  <AgentGraph
                    agents={agents}
                    onSelectAgent={handleAgentSelect}
                  />
                </div>
              )}
            </div>
          </div>
        );
      case 'createAgent':
        return (
          <CreateAgent
            onSave={handleSaveAgent}
            onCancel={handleBack}
            parentAgent={parentAgent}
          />
        );
      case 'agentDetail':
        return (
          <AgentDetail
            agent={selectedAgent}
            onBack={handleBack}
            onAddKnowledge={() => handleAddKnowledge(selectedAgent)}
            onCreateSubAgent={() => handleCreateAgent(selectedAgent)}
          />
        );
      case 'addKnowledge':
        return (
          <AddKnowledge
            agent={selectedAgent}
            onSave={handleSaveKnowledge}
            onCancel={handleBack}
          />
        );
      default:
        return <div>View not found</div>;
    }
  };

  return renderView();
};

export default KnowledgeBase;
