import { useState } from 'react';
import AgentHierarchy from './AgentHierarchy';
import AgentDetail from './AgentDetail';
import CreateAgent from './CreateAgent';
import KnowledgeManager from './KnowledgeManager';
import CRMIntegrations from './CRMIntegrations';
import '../../styles/agents-ai-new.css';

const KnowledgeBase = () => {
  // State management
  const [activeView, setActiveView] = useState('agents'); // agents, createAgent, agentDetail, knowledgeManager, crmIntegrations
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [parentAgent, setParentAgent] = useState(null); // For creating sub-agents

  // Sample agents data with hierarchy
  const [agents, setAgents] = useState([
    {
      id: 'a1',
      name: 'Assistente Supporto Clienti',
      model: 'iCodium Agata 1',
      description: 'Gestisce le richieste dei clienti e fornisce supporto tecnico avanzato',
      avatar: 'ASC',
      avatarColor: '#0e66b0',
      status: 'active',
      sources: [
        {
          id: 's1',
          name: 'Manuale di supporto tecnico',
          type: 'document-upload',
          description: 'Documentazione completa sulle procedure di supporto',
          size: '2.4 MB',
          dateAdded: '2023-05-15',
        },
        {
          id: 's2',
          name: 'FAQ Clienti',
          type: 'text-input',
          description: 'Domande frequenti dei clienti con risposte standard',
          size: '1.8 MB',
          dateAdded: '2023-06-02',
        }
      ],
      subAgents: [
        {
          id: 'a1-sub1',
          name: 'Analisi Ticket',
          model: 'iCodium Scorpion V2',
          description: 'Analizza e categorizza automaticamente i ticket in arrivo',
          avatar: 'AT',
          avatarColor: '#009b8e',
          status: 'active',
          sources: [
            {
              id: 's3',
              name: 'Categorizzazione Ticket',
              type: 'document-upload',
              description: 'Linee guida per la categorizzazione dei ticket',
              size: '1.2 MB',
              dateAdded: '2023-05-20',
            }
          ]
        },
        {
          id: 'a1-sub2',
          name: 'Risposte Automatiche',
          model: 'iCodium Maverick',
          description: 'Genera risposte automatiche personalizzate per problemi comuni',
          avatar: 'RA',
          avatarColor: '#089a44',
          status: 'active',
          sources: [
            {
              id: 's4',
              name: 'Template Risposte',
              type: 'text-input',
              description: 'Template per risposte automatiche',
              size: '0.5 MB',
              dateAdded: '2023-05-25',
            }
          ]
        }
      ]
    },
    {
      id: 'a2',
      name: 'Analista Dati Avanzato',
      model: 'iCodium Scorpion V2',
      description: 'Analizza grandi volumi di dati e genera insights strategici',
      avatar: 'ADA',
      avatarColor: '#009b8e',
      status: 'active',
      sources: [
        {
          id: 's5',
          name: 'Dataset Vendite Q1-Q3',
          type: 'code-repository',
          description: 'Repository con dati storici delle vendite e script di analisi',
          size: '15.2 MB',
          dateAdded: '2023-04-10',
        },
        {
          id: 's6',
          name: 'Documentazione API Analytics',
          type: 'web-crawling',
          description: 'Documentazione completa delle API di analytics',
          size: '3.8 MB',
          dateAdded: '2023-05-01',
        }
      ],
      subAgents: [
        {
          id: 'a2-sub1',
          name: 'Predictor Vendite',
          model: 'iCodium Maverick',
          description: 'Modello predittivo per le vendite future',
          avatar: 'PV',
          avatarColor: '#089a44',
          status: 'active',
          sources: [
            {
              id: 's7',
              name: 'Modelli ML Vendite',
              type: 'code-repository',
              description: 'Repository con modelli di machine learning per predizioni',
              size: '8.5 MB',
              dateAdded: '2023-05-15',
            }
          ]
        }
      ]
    },
    {
      id: 'a3',
      name: 'Content Creator Pro',
      model: 'iCodium Agata 1',
      description: 'Crea contenuti multimediali per marketing e comunicazione aziendale',
      avatar: 'CCP',
      avatarColor: '#089a44',
      status: 'active',
      sources: [
        {
          id: 's8',
          name: 'Brand Guidelines 2024',
          type: 'document-upload',
          description: 'Linee guida aggiornate del brand per la creazione di contenuti',
          size: '4.1 MB',
          dateAdded: '2023-03-25',
        }
      ],
      subAgents: [
        {
          id: 'a3-sub1',
          name: 'Social Media Manager',
          model: 'iCodium Scorpion V2',
          description: 'Gestisce e ottimizza i contenuti per tutti i canali social',
          avatar: 'SMM',
          avatarColor: '#e74c3c',
          status: 'active',
          sources: []
        }
      ]
    }
  ]);

  // Handle agent selection
  const handleAgentSelect = (agent) => {
    setSelectedAgent(agent);
    setActiveView('agentDetail');
  };

  // Handle creating a new agent
  const handleCreateAgent = (parent = null) => {
    setParentAgent(parent);
    setActiveView('createAgent');
  };

  // Handle creating sub-agent
  const handleCreateSubAgent = (parentAgent) => {
    setParentAgent(parentAgent);
    setActiveView('createAgent');
  };

  // Handle back navigation
  const handleBack = () => {
    setActiveView('agents');
    setSelectedAgent(null);
    setParentAgent(null);
  };

  // Handle knowledge management
  const handleKnowledgeManager = () => {
    setActiveView('knowledgeManager');
  };

  // Handle CRM integrations
  const handleCRMIntegrations = () => {
    setActiveView('crmIntegrations');
  };

  // Handle saving a new agent
  const handleSaveAgent = (newAgent) => {
    if (parentAgent) {
      // Creating a sub-agent
      const updatedAgents = agents.map(agent => {
        if (agent.id === parentAgent.id) {
          return {
            ...agent,
            subAgents: [
              ...(agent.subAgents || []),
              {
                ...newAgent,
                id: `${agent.id}-sub${(agent.subAgents?.length || 0) + 1}`,
                sources: []
              }
            ]
          };
        }
        return agent;
      });
      setAgents(updatedAgents);
    } else {
      // Creating a main agent
      setAgents([...agents, {
        ...newAgent,
        id: `a${agents.length + 1}`,
        sources: [],
        subAgents: []
      }]);
    }
    setParentAgent(null);
    setActiveView('agents');
  };

  // Handle saving knowledge
  const handleSaveKnowledge = (knowledgeData) => {
    // Update the agent with new knowledge source
    const updatedAgents = agents.map(agent => {
      if (agent.id === knowledgeData.agentId) {
        return {
          ...agent,
          sources: [...(agent.sources || []), knowledgeData]
        };
      }
      // Check sub-agents
      if (agent.subAgents) {
        const updatedSubAgents = agent.subAgents.map(subAgent => {
          if (subAgent.id === knowledgeData.agentId) {
            return {
              ...subAgent,
              sources: [...(subAgent.sources || []), knowledgeData]
            };
          }
          return subAgent;
        });
        return { ...agent, subAgents: updatedSubAgents };
      }
      return agent;
    });

    setAgents(updatedAgents);
    setActiveView('agents');
  };

  // Render main content based on active view
  const renderContent = () => {
    switch (activeView) {
      case 'createAgent':
        return (
          <CreateAgent
            onSave={handleSaveAgent}
            onCancel={handleBack}
            parentAgent={parentAgent}
          />
        );
      case 'agentDetail':
        return (
          <AgentDetail
            agent={selectedAgent}
            onBack={handleBack}
            onAddKnowledge={() => setActiveView('knowledgeManager')}
          />
        );
      case 'knowledgeManager':
        return (
          <KnowledgeManager
            agents={agents}
            onBack={handleBack}
            onSaveKnowledge={handleSaveKnowledge}
          />
        );
      case 'crmIntegrations':
        return (
          <CRMIntegrations
            onBack={handleBack}
          />
        );
      default:
        return (
          <div className="agents-ai-container">
            <div className="agents-ai-header">
              <div className="agents-ai-title">
                <div className="agents-ai-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M9 12l2 2 4-4"></path>
                    <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                    <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                    <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                    <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                  </svg>
                </div>
                <h1>Agenti AI</h1>
              </div>
              <div className="agents-ai-actions">
                <button className="btn btn-secondary" onClick={handleKnowledgeManager}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                  Gestisci Conoscenza
                </button>
                <button className="btn btn-secondary" onClick={handleCRMIntegrations}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  </svg>
                  Integrazioni CRM
                </button>
                <button className="btn btn-primary" onClick={handleCreateAgent}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Nuovo Agente
                </button>
              </div>
            </div>

            <div className="agents-ai-nav">
              <button className="nav-tab active">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                Gerarchia Agenti
              </button>
            </div>

            <div className="agents-ai-content">
              <AgentHierarchy
                agents={agents}
                onSelectAgent={handleAgentSelect}
                onCreateAgent={handleCreateAgent}
                onCreateSubAgent={handleCreateSubAgent}
              />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="knowledge-base">
      {renderContent()}
    </div>
  );
};

export default KnowledgeBase;