import React, { useState } from 'react';

const AgentHierarchy = ({ agents, onSelectAgent, onCreateAgent, onCreateSubAgent }) => {
  const [expandedAgents, setExpandedAgents] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // all, active, inactive

  // Toggle expanded state for an agent
  const toggleExpand = (agentId, e) => {
    e.stopPropagation();
    setExpandedAgents(prev => ({
      ...prev,
      [agentId]: !prev[agentId]
    }));
  };

  // Filter agents based on search and status
  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.model.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || agent.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Calculate statistics
  const totalAgents = agents.length;
  const totalSubAgents = agents.reduce((sum, agent) => sum + (agent.subAgents?.length || 0), 0);
  const activeAgents = agents.filter(agent => agent.status === 'active').length;
  const totalKnowledgeSources = agents.reduce((sum, agent) => {
    const agentSources = agent.sources?.length || 0;
    const subAgentSources = agent.subAgents?.reduce((subSum, subAgent) =>
      subSum + (subAgent.sources?.length || 0), 0) || 0;
    return sum + agentSources + subAgentSources;
  }, 0);

  // Render agent status
  const renderStatus = (status) => (
    <div className="agent-status">
      <div className={`status-indicator ${status}`}></div>
      <span className="status-text">
        {status === 'active' ? 'Attivo' : 'Inattivo'}
      </span>
    </div>
  );

  // Render agent metrics
  const renderMetrics = (agent) => {
    const sourcesCount = agent.sources?.length || 0;
    const subAgentsCount = agent.subAgents?.length || 0;

    return (
      <div className="agent-metrics">
        <div className="metric-item">
          <svg className="metric-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
          </svg>
          <div>
            <div className="metric-value">{sourcesCount}</div>
            <div className="metric-label">Fonti</div>
          </div>
        </div>

        <div className="metric-item">
          <svg className="metric-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <div>
            <div className="metric-value">{subAgentsCount}</div>
            <div className="metric-label">Sub-agenti</div>
          </div>
        </div>

        <div className="metric-item">
          <svg className="metric-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
          </svg>
          <div>
            <div className="metric-value">98%</div>
            <div className="metric-label">Efficienza</div>
          </div>
        </div>
      </div>
    );
  };

  // Render sub-agents
  const renderSubAgents = (subAgents, parentAgent) => {
    if (!subAgents || subAgents.length === 0) return null;

    return (
      <div className="subagents-section">
        <div className="subagents-header">
          <h4 className="subagents-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            Sub-agenti ({subAgents.length})
          </h4>
          <button
            className="btn btn-outline btn-icon"
            onClick={(e) => {
              e.stopPropagation();
              onCreateSubAgent(parentAgent);
            }}
            title="Aggiungi sub-agente"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </button>
        </div>

        <div className="subagents-list">
          {subAgents.map(subAgent => (
            <div
              key={subAgent.id}
              className="subagent-card"
              onClick={(e) => {
                e.stopPropagation();
                onSelectAgent(subAgent);
              }}
            >
              <div className="subagent-header">
                <div
                  className="subagent-avatar"
                  style={{ backgroundColor: subAgent.avatarColor || '#009b8e' }}
                >
                  {subAgent.avatar && subAgent.avatar.startsWith('/') ? (
                    <img src={subAgent.avatar} alt={subAgent.name} />
                  ) : (
                    subAgent.avatar || subAgent.name.charAt(0)
                  )}
                </div>
                <div className="subagent-info">
                  <div className="subagent-name">{subAgent.name}</div>
                  <div className="subagent-model">{subAgent.model}</div>
                </div>
                {renderStatus(subAgent.status)}
              </div>

              <div className="subagent-description">
                {subAgent.description}
              </div>

              <div className="subagent-metrics">
                <div className="subagent-metric">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                  </svg>
                  {subAgent.sources?.length || 0} fonti
                </div>
                <div className="subagent-metric">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  Ultimo aggiornamento: 2h fa
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render main agent card
  const renderAgentCard = (agent) => {
    const isExpanded = expandedAgents[agent.id];
    const hasSubAgents = agent.subAgents && agent.subAgents.length > 0;

    return (
      <div key={agent.id} className="agent-card">
        <div
          className="agent-card-header"
          onClick={() => onSelectAgent(agent)}
        >
          <div
            className="agent-avatar"
            style={{ backgroundColor: agent.avatarColor || '#0e66b0' }}
          >
            {agent.avatar && agent.avatar.startsWith('/') ? (
              <img src={agent.avatar} alt={agent.name} />
            ) : (
              agent.avatar || agent.name.charAt(0)
            )}
          </div>

          <div className="agent-info">
            <div className="agent-name">{agent.name}</div>
            <div className="agent-model">{agent.model}</div>
            <div className="agent-description">{agent.description}</div>
          </div>

          {renderStatus(agent.status)}

          {hasSubAgents && (
            <button
              className="btn btn-outline btn-icon"
              onClick={(e) => toggleExpand(agent.id, e)}
              title={isExpanded ? 'Comprimi' : 'Espandi'}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                style={{ transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.3s' }}
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>
          )}
        </div>

        <div className="agent-card-body">
          {renderMetrics(agent)}

          {hasSubAgents && isExpanded && renderSubAgents(agent.subAgents, agent)}
        </div>
      </div>
    );
  };

  // Render create agent card
  const renderCreateAgentCard = () => (
    <div className="create-agent-card" onClick={() => onCreateAgent()}>
      <div className="create-agent-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </div>
      <h3 className="create-agent-title">Crea nuovo agente</h3>
      <p className="create-agent-description">
        Aggiungi un nuovo agente AI per espandere le capacità del sistema
      </p>
    </div>
  );

  return (
    <div className="agent-hierarchy-container">
      <div className="hierarchy-header">
        <div className="hierarchy-controls">
          <div className="search-bar">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              placeholder="Cerca agenti..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filter-controls">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">Tutti gli stati</option>
              <option value="active">Solo attivi</option>
              <option value="inactive">Solo inattivi</option>
            </select>
          </div>
        </div>

        <div className="hierarchy-stats">
          <div className="stat-card">
            <div className="stat-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </div>
            <div className="stat-content">
              <div className="stat-value">{totalAgents}</div>
              <div className="stat-label">Agenti principali</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
              </svg>
            </div>
            <div className="stat-content">
              <div className="stat-value">{totalSubAgents}</div>
              <div className="stat-label">Sub-agenti</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="8 12 12 16 16 12"></polyline>
                <line x1="12" y1="8" x2="12" y2="16"></line>
              </svg>
            </div>
            <div className="stat-content">
              <div className="stat-value">{activeAgents}</div>
              <div className="stat-label">Agenti attivi</div>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
              </svg>
            </div>
            <div className="stat-content">
              <div className="stat-value">{totalKnowledgeSources}</div>
              <div className="stat-label">Fonti di conoscenza</div>
            </div>
          </div>
        </div>
      </div>

      <div className="agent-hierarchy-list">
        {filteredAgents.length > 0 ? (
          <>
            {filteredAgents.map(agent => renderAgentCard(agent))}
            {renderCreateAgentCard()}
          </>
        ) : (
          <div className="no-results">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <h3>Nessun agente trovato</h3>
            <p>Prova a modificare i criteri di ricerca o i filtri</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentHierarchy;
