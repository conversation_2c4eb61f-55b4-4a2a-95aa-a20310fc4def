import React, { useState } from 'react';

const AgentList = ({ agents, onSelectAgent, onCreateAgent, displayMode = 'list' }) => {
  const [expandedAgents, setExpandedAgents] = useState({});

  // Toggle expanded state for an agent
  const toggleExpand = (agentId, e) => {
    e.stopPropagation();
    setExpandedAgents(prev => ({
      ...prev,
      [agentId]: !prev[agentId]
    }));
  };

  // Render a single agent card
  const renderAgentCard = (agent) => {
    const hasSubAgents = agent.subAgents && agent.subAgents.length > 0;
    const isExpanded = expandedAgents[agent.id];
    const sourcesCount = agent.sources ? agent.sources.length : 0;

    return (
      <div key={agent.id} className="agent-card" onClick={() => onSelectAgent(agent)}>
        <div className="agent-card-header">
          <div
            className="agent-avatar"
            style={{ backgroundColor: agent.avatarColor || '#0e66b0' }}
          >
            {agent.avatar && agent.avatar.startsWith('/') ? (
              <img src={agent.avatar} alt={agent.name} />
            ) : (
              agent.avatar || agent.name.charAt(0)
            )}
          </div>
          <div className="agent-header-content">
            <div className="agent-name">{agent.name}</div>
            {agent.model && <div className="agent-model">{agent.model}</div>}
          </div>
        </div>

        <div className="agent-card-body">
          <div className="agent-description">{agent.description}</div>
          <div className="agent-meta">
            <div className="agent-meta-item">
              <span className={`agents-ai-status ${agent.status}`}></span>
              {agent.status === 'active' ? 'Attivo' : 'Inattivo'}
            </div>
            {sourcesCount > 0 && (
              <div className="agent-meta-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                {sourcesCount} {sourcesCount === 1 ? 'fonte' : 'fonti'}
              </div>
            )}
            {hasSubAgents && (
              <div className="agent-meta-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                {agent.subAgents.length} {agent.subAgents.length === 1 ? 'sub-agente' : 'sub-agenti'}
              </div>
            )}
          </div>
        </div>

        <div className="agent-card-footer">
          {hasSubAgents && (
            <button
              className="agent-action-button"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpand(agent.id, e);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {isExpanded ? (
                  <polyline points="18 15 12 9 6 15"></polyline>
                ) : (
                  <polyline points="6 9 12 15 18 9"></polyline>
                )}
              </svg>
              {isExpanded ? 'Nascondi sub-agenti' : 'Mostra sub-agenti'}
            </button>
          )}
          <button
            className="agent-action-button"
            onClick={(e) => {
              e.stopPropagation();
              onCreateAgent(agent);
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="8.5" cy="7" r="4"></circle>
              <line x1="20" y1="8" x2="20" y2="14"></line>
              <line x1="23" y1="11" x2="17" y2="11"></line>
            </svg>
            Crea sub-agente
          </button>
        </div>

        {/* Render sub-agents if expanded */}
        {hasSubAgents && isExpanded && (
          <div className="subagents-container">
            {agent.subAgents.map(subAgent => (
              <div
                key={subAgent.id}
                className="subagent-card"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelectAgent(subAgent);
                }}
              >
                <div className="subagent-card-header">
                  <div
                    className="subagent-avatar"
                    style={{ backgroundColor: subAgent.avatarColor || '#009b8e' }}
                  >
                    {subAgent.avatar && subAgent.avatar.startsWith('/') ? (
                      <img src={subAgent.avatar} alt={subAgent.name} />
                    ) : (
                      subAgent.avatar || subAgent.name.charAt(0)
                    )}
                  </div>
                  <div className="subagent-header-content">
                    <div className="subagent-name">{subAgent.name}</div>
                    {subAgent.model && <div className="subagent-model">{subAgent.model}</div>}
                  </div>
                </div>

                <div className="subagent-card-body">
                  <div className="subagent-description">{subAgent.description}</div>
                  <div className="subagent-meta">
                    <div className="subagent-meta-item">
                      <span className={`agents-ai-status ${subAgent.status}`}></span>
                      {subAgent.status === 'active' ? 'Attivo' : 'Inattivo'}
                    </div>
                    {subAgent.sources && subAgent.sources.length > 0 && (
                      <div className="subagent-meta-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                          <line x1="16" y1="13" x2="8" y2="13"></line>
                          <line x1="16" y1="17" x2="8" y2="17"></line>
                          <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        {subAgent.sources.length} {subAgent.sources.length === 1 ? 'fonte' : 'fonti'}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render create agent card
  const renderCreateAgentCard = () => (
    <div className="create-agent-card" onClick={() => onCreateAgent()}>
      <div className="create-agent-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </div>
      <div className="create-agent-title">Crea nuovo agente</div>
      <div className="create-agent-description">
        Configura un agente AI personalizzato per la tua knowledge base
      </div>
    </div>
  );

  return (
    <div className="agent-list-container">
      <div className={`agent-list ${displayMode === 'grid' ? 'grid-view' : ''}`}>
        {agents.map(agent => renderAgentCard(agent))}
        {renderCreateAgentCard()}
      </div>
    </div>
  );
};

export default AgentList;
