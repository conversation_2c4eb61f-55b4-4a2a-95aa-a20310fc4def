import React, { useState } from 'react';

const KnowledgeManager = ({ agents, onBack, onSaveKnowledge }) => {
  const [activeTab, setActiveTab] = useState('web-crawling');
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'web-crawling',
    content: '',
    url: '',
    files: [],
    settings: {}
  });

  // Knowledge source types
  const knowledgeTypes = [
    {
      id: 'web-crawling',
      name: 'Web Crawling',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="2" y1="12" x2="22" y2="12"></line>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
        </svg>
      ),
      description: 'Estrai contenuti da siti web e pagine online'
    },
    {
      id: 'code-repository',
      name: 'Repository di Codice',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polyline points="16 18 22 12 16 6"></polyline>
          <polyline points="8 6 2 12 8 18"></polyline>
        </svg>
      ),
      description: 'Carica e analizza repository di codice sorgente'
    },
    {
      id: 'document-upload',
      name: 'Documenti PDF',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
        </svg>
      ),
      description: 'Carica e processa documenti PDF'
    },
    {
      id: 'audio-content',
      name: 'Contenuti Audio',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>
        </svg>
      ),
      description: 'Carica file audio per trascrizione e analisi'
    },
    {
      id: 'video-content',
      name: 'Contenuti Video',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polygon points="23 7 16 12 23 17 23 7"></polygon>
          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
        </svg>
      ),
      description: 'Carica video per estrazione di contenuti'
    },
    {
      id: 'text-input',
      name: 'Input Testuale',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
        </svg>
      ),
      description: 'Inserisci direttamente contenuti testuali'
    }
  ];

  // Get all agents (including sub-agents) for selection
  const getAllAgents = () => {
    const allAgents = [];
    agents.forEach(agent => {
      allAgents.push({ ...agent, type: 'main' });
      if (agent.subAgents) {
        agent.subAgents.forEach(subAgent => {
          allAgents.push({ ...subAgent, type: 'sub', parentName: agent.name });
        });
      }
    });
    return allAgents;
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle file upload
  const handleFileUpload = (files) => {
    setFormData(prev => ({
      ...prev,
      files: Array.from(files)
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedAgent) {
      alert('Seleziona un agente per associare la conoscenza');
      return;
    }

    const knowledgeData = {
      ...formData,
      agentId: selectedAgent.id,
      dateAdded: new Date().toISOString().split('T')[0],
      id: Date.now().toString()
    };

    onSaveKnowledge(knowledgeData);
  };

  // Render knowledge type content
  const renderKnowledgeTypeContent = () => {
    switch (activeTab) {
      case 'web-crawling':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">URL del sito web</label>
              <input
                type="url"
                className="form-input"
                placeholder="https://example.com"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Profondità di crawling</label>
              <select 
                className="form-select"
                onChange={(e) => handleInputChange('settings', { ...formData.settings, depth: e.target.value })}
              >
                <option value="1">1 livello (solo pagina principale)</option>
                <option value="2">2 livelli</option>
                <option value="3">3 livelli</option>
                <option value="unlimited">Illimitato</option>
              </select>
            </div>
            <div className="form-group">
              <label className="form-label">Filtri di contenuto</label>
              <div className="checkbox-group">
                <label className="checkbox-label">
                  <input type="checkbox" /> Includi immagini
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Includi link esterni
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Escludi navigazione
                </label>
              </div>
            </div>
          </div>
        );

      case 'code-repository':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">Repository URL (Git)</label>
              <input
                type="url"
                className="form-input"
                placeholder="https://github.com/user/repo.git"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Branch</label>
              <input
                type="text"
                className="form-input"
                placeholder="main"
                defaultValue="main"
              />
            </div>
            <div className="form-group">
              <label className="form-label">Linguaggi di programmazione</label>
              <div className="checkbox-group">
                <label className="checkbox-label">
                  <input type="checkbox" /> JavaScript
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Python
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Java
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> C++
                </label>
              </div>
            </div>
          </div>
        );

      case 'document-upload':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">Carica documenti PDF</label>
              <div className="file-upload-area">
                <input
                  type="file"
                  multiple
                  accept=".pdf"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="file-input"
                />
                <div className="file-upload-content">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                  <p>Trascina i file PDF qui o clicca per selezionare</p>
                  <p className="file-upload-hint">Supporta file fino a 50MB</p>
                </div>
              </div>
            </div>
            {formData.files.length > 0 && (
              <div className="uploaded-files">
                <h4>File caricati:</h4>
                {formData.files.map((file, index) => (
                  <div key={index} className="uploaded-file">
                    <span>{file.name}</span>
                    <span>{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case 'audio-content':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">Carica file audio</label>
              <div className="file-upload-area">
                <input
                  type="file"
                  multiple
                  accept=".mp3,.wav,.m4a,.ogg"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="file-input"
                />
                <div className="file-upload-content">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                  </svg>
                  <p>Trascina i file audio qui o clicca per selezionare</p>
                  <p className="file-upload-hint">Supporta MP3, WAV, M4A, OGG</p>
                </div>
              </div>
            </div>
            <div className="form-group">
              <label className="form-label">Lingua del contenuto</label>
              <select className="form-select">
                <option value="it">Italiano</option>
                <option value="en">Inglese</option>
                <option value="fr">Francese</option>
                <option value="de">Tedesco</option>
                <option value="es">Spagnolo</option>
              </select>
            </div>
          </div>
        );

      case 'video-content':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">Carica file video</label>
              <div className="file-upload-area">
                <input
                  type="file"
                  multiple
                  accept=".mp4,.avi,.mov,.mkv"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="file-input"
                />
                <div className="file-upload-content">
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="23 7 16 12 23 17 23 7"></polygon>
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>
                  </svg>
                  <p>Trascina i file video qui o clicca per selezionare</p>
                  <p className="file-upload-hint">Supporta MP4, AVI, MOV, MKV</p>
                </div>
              </div>
            </div>
            <div className="form-group">
              <label className="form-label">Estrai contenuto</label>
              <div className="checkbox-group">
                <label className="checkbox-label">
                  <input type="checkbox" defaultChecked /> Audio/Trascrizione
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Sottotitoli
                </label>
                <label className="checkbox-label">
                  <input type="checkbox" /> Frame chiave
                </label>
              </div>
            </div>
          </div>
        );

      case 'text-input':
        return (
          <div className="knowledge-form-section">
            <div className="form-group">
              <label className="form-label">Contenuto testuale</label>
              <textarea
                className="form-textarea"
                rows="10"
                placeholder="Inserisci il contenuto testuale qui..."
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Formato del testo</label>
              <select className="form-select">
                <option value="plain">Testo semplice</option>
                <option value="markdown">Markdown</option>
                <option value="html">HTML</option>
                <option value="json">JSON</option>
              </select>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="knowledge-manager-container">
      <div className="knowledge-manager-header">
        <button className="btn btn-secondary" onClick={onBack}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Indietro
        </button>
        <h1>Gestione Conoscenza</h1>
      </div>

      <div className="knowledge-manager-content">
        <div className="knowledge-types-grid">
          {knowledgeTypes.map(type => (
            <div
              key={type.id}
              className={`knowledge-type-card ${activeTab === type.id ? 'active' : ''}`}
              onClick={() => setActiveTab(type.id)}
            >
              <div className="knowledge-type-icon">
                {type.icon}
              </div>
              <h3 className="knowledge-type-name">{type.name}</h3>
              <p className="knowledge-type-description">{type.description}</p>
            </div>
          ))}
        </div>

        <div className="knowledge-form-container">
          <form onSubmit={handleSubmit} className="knowledge-form">
            <div className="form-header">
              <h2>Aggiungi {knowledgeTypes.find(t => t.id === activeTab)?.name}</h2>
            </div>

            <div className="form-group">
              <label className="form-label">Nome della fonte</label>
              <input
                type="text"
                className="form-input"
                placeholder="Es. Documentazione API"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">Descrizione</label>
              <textarea
                className="form-textarea"
                rows="3"
                placeholder="Descrivi brevemente il contenuto di questa fonte"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>

            {renderKnowledgeTypeContent()}

            <div className="form-group">
              <label className="form-label">Associa ad agente</label>
              <select
                className="form-select"
                value={selectedAgent?.id || ''}
                onChange={(e) => {
                  const agent = getAllAgents().find(a => a.id === e.target.value);
                  setSelectedAgent(agent);
                }}
                required
              >
                <option value="">Seleziona un agente</option>
                {getAllAgents().map(agent => (
                  <option key={agent.id} value={agent.id}>
                    {agent.type === 'sub' ? `${agent.parentName} → ${agent.name}` : agent.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-actions">
              <button type="button" className="btn btn-secondary" onClick={onBack}>
                Annulla
              </button>
              <button type="submit" className="btn btn-primary">
                Salva Conoscenza
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeManager;
