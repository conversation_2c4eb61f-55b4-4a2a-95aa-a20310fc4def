import React, { useState, useRef } from 'react';
import '../../styles/add-knowledge.css';

const AddKnowledge = ({ agent, onSave, onCancel }) => {
  const [activeSourceType, setActiveSourceType] = useState('document');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'pdf',
    file: null,
    url: '',
    text: ''
  });

  const [errors, setErrors] = useState({});
  const [filePreview, setFilePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Handle source type selection
  const handleSourceTypeChange = (type) => {
    setActiveSourceType(type);

    // Update form type based on source type
    if (type === 'document') {
      setFormData({
        ...formData,
        type: 'pdf'
      });
    } else if (type === 'webpage') {
      setFormData({
        ...formData,
        type: 'webpage'
      });
    } else if (type === 'text') {
      setFormData({
        ...formData,
        type: 'text'
      });
    }
  };

  // Handle file selection
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Update form data
    setFormData({
      ...formData,
      file: file,
      name: formData.name || file.name.split('.')[0] // Use filename as default name if empty
    });

    // Create file preview
    setFilePreview({
      name: file.name,
      size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
      type: file.type
    });

    // Clear error
    if (errors.file) {
      setErrors({
        ...errors,
        file: null
      });
    }
  };

  // Handle file drop
  const handleFileDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const file = e.dataTransfer.files[0];
    if (!file) return;

    // Update form data
    setFormData({
      ...formData,
      file: file,
      name: formData.name || file.name.split('.')[0] // Use filename as default name if empty
    });

    // Create file preview
    setFilePreview({
      name: file.name,
      size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
      type: file.type
    });

    // Clear error
    if (errors.file) {
      setErrors({
        ...errors,
        file: null
      });
    }
  };

  // Handle file drag over
  const handleFileDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Handle file removal
  const handleFileRemove = () => {
    setFormData({
      ...formData,
      file: null
    });
    setFilePreview(null);
  };

  // Handle URL check
  const handleUrlCheck = () => {
    if (!formData.url.trim()) return;

    // Simulate URL preview loading
    setIsUploading(true);

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);

      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        setUploadProgress(0);
      }
    }, 200);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio';
    }

    if (activeSourceType === 'document' && !formData.file) {
      newErrors.file = 'Il file è obbligatorio';
    } else if (activeSourceType === 'webpage' && !formData.url.trim()) {
      newErrors.url = 'L\'URL è obbligatorio';
    } else if (activeSourceType === 'text' && !formData.text.trim()) {
      newErrors.text = 'Il testo è obbligatorio';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Simulate upload process
    setIsUploading(true);

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      setUploadProgress(progress);

      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        setUploadProgress(0);

        // For demo purposes, we'll simulate a file upload
        const sourceData = {
          name: formData.name,
          description: formData.description,
          type: activeSourceType === 'document' ? formData.type : activeSourceType,
          size: filePreview ? filePreview.size : '0.8 MB', // Simulated file size
          dateAdded: new Date().toISOString().split('T')[0]
        };

        // Save knowledge source
        onSave(sourceData);
      }
    }, 100);
  };

  return (
    <div className="knowledge-content">
      <div className="knowledge-header">
        <button className="btn btn-secondary" onClick={onCancel}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Indietro
        </button>
        <div className="knowledge-title">Aggiungi conoscenza a {agent.name}</div>
      </div>

      <div className="add-knowledge-form">
        <div className="source-type-selector">
          <div
            className={`source-type-option ${activeSourceType === 'document' ? 'active' : ''}`}
            onClick={() => handleSourceTypeChange('document')}
          >
            <div className="source-type-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </div>
            <div className="source-type-label">Documento</div>
          </div>

          <div
            className={`source-type-option ${activeSourceType === 'webpage' ? 'active' : ''}`}
            onClick={() => handleSourceTypeChange('webpage')}
          >
            <div className="source-type-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="2" y1="12" x2="22" y2="12"></line>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </div>
            <div className="source-type-label">Pagina Web</div>
          </div>

          <div
            className={`source-type-option ${activeSourceType === 'text' ? 'active' : ''}`}
            onClick={() => handleSourceTypeChange('text')}
          >
            <div className="source-type-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="17" y1="10" x2="3" y2="10"></line>
                <line x1="21" y1="6" x2="3" y2="6"></line>
                <line x1="21" y1="14" x2="3" y2="14"></line>
                <line x1="17" y1="18" x2="3" y2="18"></line>
              </svg>
            </div>
            <div className="source-type-label">Testo</div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Nome*</label>
            <input
              type="text"
              className={`form-input ${errors.name ? 'error' : ''}`}
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Es. Manuale di supporto tecnico"
            />
            {errors.name && <div className="form-error">{errors.name}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Descrizione (opzionale)</label>
            <textarea
              className="form-textarea"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Descrivi brevemente questa fonte di conoscenza"
            ></textarea>
          </div>

          {activeSourceType === 'document' && (
            <div className="form-group">
              <label className="form-label">Carica documento*</label>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                accept=".pdf,.docx,.txt"
              />

              {!filePreview ? (
                <div
                  className={`upload-area ${isUploading ? 'active' : ''}`}
                  onClick={() => fileInputRef.current.click()}
                  onDrop={handleFileDrop}
                  onDragOver={handleFileDragOver}
                >
                  <div className="upload-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                  </div>
                  <div className="upload-text">Trascina un documento o clicca per caricare</div>
                  <div className="upload-hint">PDF, DOCX, TXT, max 50MB</div>
                </div>
              ) : (
                <div className="file-preview">
                  <div className="file-preview-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                  </div>
                  <div className="file-preview-info">
                    <div className="file-preview-name">{filePreview.name}</div>
                    <div className="file-preview-meta">{filePreview.size} - {filePreview.type}</div>
                  </div>
                  <button
                    type="button"
                    className="file-preview-remove"
                    onClick={handleFileRemove}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              )}

              {isUploading && (
                <div className="upload-progress">
                  <div
                    className="upload-progress-bar"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                  <div className="upload-status">
                    <span>Caricamento in corso...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                </div>
              )}
              {errors.file && <div className="form-error">{errors.file}</div>}

              <div className="form-group" style={{ marginTop: '20px' }}>
                <label className="form-label">Tipo di documento</label>
                <select
                  className="form-select"
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                >
                  <option value="pdf">PDF</option>
                  <option value="docx">DOCX</option>
                  <option value="txt">TXT</option>
                </select>
              </div>
            </div>
          )}

          {activeSourceType === 'webpage' && (
            <div className="form-group">
              <label className="form-label">URL della pagina web*</label>
              <input
                type="url"
                className={`form-input ${errors.url ? 'error' : ''}`}
                name="url"
                value={formData.url}
                onChange={handleChange}
                placeholder="https://example.com/pagina"
              />
              {errors.url && <div className="form-error">{errors.url}</div>}

              <div className="form-group" style={{ marginTop: '20px' }}>
                <label className="form-label">Opzioni di crawling</label>
                <div className="form-checkbox">
                  <input type="checkbox" id="crawl-subpages" />
                  <label htmlFor="crawl-subpages">Crawl delle sottopagine</label>
                </div>
                <div className="form-checkbox">
                  <input type="checkbox" id="follow-links" />
                  <label htmlFor="follow-links">Segui i link esterni</label>
                </div>
                <div className="form-checkbox">
                  <input type="checkbox" id="extract-text" checked readOnly />
                  <label htmlFor="extract-text">Estrai solo il testo</label>
                </div>
              </div>
            </div>
          )}

          {activeSourceType === 'text' && (
            <div className="form-group">
              <label className="form-label">Testo*</label>
              <textarea
                className={`form-textarea ${errors.text ? 'error' : ''}`}
                name="text"
                value={formData.text}
                onChange={handleChange}
                placeholder="Inserisci il testo che vuoi aggiungere come fonte di conoscenza"
                style={{ minHeight: '200px' }}
              ></textarea>
              {errors.text && <div className="form-error">{errors.text}</div>}
            </div>
          )}

          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>Annulla</button>
            <button type="submit" className="btn btn-primary">Aggiungi conoscenza</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddKnowledge;
