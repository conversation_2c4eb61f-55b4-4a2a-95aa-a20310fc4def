import React, { useState } from 'react';

const CRMIntegrations = ({ onBack }) => {
  const [activeIntegration, setActiveIntegration] = useState(null);
  const [integrations, setIntegrations] = useState([
    {
      id: 'salesforce',
      name: 'Salesforce',
      description: 'Integra con Salesforce CRM per gestire lead, opportunità e clienti',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
          <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
          <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
          <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
          <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
          <line x1="12" y1="22.08" x2="12" y2="12"></line>
        </svg>
      ),
      status: 'disconnected',
      features: ['Lead Management', 'Opportunity Tracking', 'Contact Sync', 'Custom Fields'],
      color: '#00A1E0'
    },
    {
      id: 'hubspot',
      name: 'HubSpot',
      description: 'Connetti con HubSpot per marketing automation e gestione contatti',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"></circle>
          <circle cx="12" cy="12" r="6"></circle>
          <circle cx="12" cy="12" r="2"></circle>
        </svg>
      ),
      status: 'connected',
      features: ['Marketing Automation', 'Email Campaigns', 'Analytics', 'Pipeline Management'],
      color: '#FF7A59'
    },
    {
      id: 'pipedrive',
      name: 'Pipedrive',
      description: 'Integrazione con Pipedrive per gestione pipeline e vendite',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 3v18h18"></path>
          <path d="M18.7 8a3 3 0 0 0-5.4 0l-3 4a3 3 0 0 0 5.4 0l3-4z"></path>
        </svg>
      ),
      status: 'disconnected',
      features: ['Sales Pipeline', 'Deal Tracking', 'Activity Management', 'Reporting'],
      color: '#1B5BF7'
    },
    {
      id: 'zoho',
      name: 'Zoho CRM',
      description: 'Connetti con Zoho CRM per gestione completa dei clienti',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
          <line x1="8" y1="21" x2="16" y2="21"></line>
          <line x1="12" y1="17" x2="12" y2="21"></line>
        </svg>
      ),
      status: 'disconnected',
      features: ['Customer Management', 'Sales Automation', 'Workflow Rules', 'Custom Modules'],
      color: '#C8102E'
    },
    {
      id: 'microsoft-dynamics',
      name: 'Microsoft Dynamics 365',
      description: 'Integrazione con Microsoft Dynamics 365 per enterprise CRM',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <rect x="7" y="7" width="3" height="9"></rect>
          <rect x="14" y="7" width="3" height="5"></rect>
        </svg>
      ),
      status: 'disconnected',
      features: ['Enterprise CRM', 'Advanced Analytics', 'AI Insights', 'Integration Hub'],
      color: '#0078D4'
    },
    {
      id: 'custom-api',
      name: 'API Personalizzata',
      description: 'Connetti il tuo CRM personalizzato tramite API REST',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polyline points="16 18 22 12 16 6"></polyline>
          <polyline points="8 6 2 12 8 18"></polyline>
        </svg>
      ),
      status: 'disconnected',
      features: ['Custom Endpoints', 'Flexible Schema', 'Webhook Support', 'Authentication'],
      color: '#6B46C1'
    }
  ]);

  const [configForm, setConfigForm] = useState({
    apiKey: '',
    apiSecret: '',
    instanceUrl: '',
    webhookUrl: '',
    syncFrequency: 'hourly',
    dataMapping: {}
  });

  // Handle integration connection
  const handleConnect = (integration) => {
    setActiveIntegration(integration);
  };

  // Handle integration disconnection
  const handleDisconnect = (integrationId) => {
    setIntegrations(prev => prev.map(int => 
      int.id === integrationId 
        ? { ...int, status: 'disconnected' }
        : int
    ));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Update integration status
    setIntegrations(prev => prev.map(int => 
      int.id === activeIntegration.id 
        ? { ...int, status: 'connected' }
        : int
    ));
    
    setActiveIntegration(null);
    setConfigForm({
      apiKey: '',
      apiSecret: '',
      instanceUrl: '',
      webhookUrl: '',
      syncFrequency: 'hourly',
      dataMapping: {}
    });
  };

  // Render integration card
  const renderIntegrationCard = (integration) => (
    <div key={integration.id} className="crm-integration-card">
      <div className="integration-header">
        <div 
          className="integration-icon"
          style={{ backgroundColor: integration.color }}
        >
          {integration.icon}
        </div>
        <div className="integration-info">
          <h3 className="integration-name">{integration.name}</h3>
          <p className="integration-description">{integration.description}</p>
        </div>
        <div className="integration-status">
          <div className={`status-indicator ${integration.status}`}></div>
          <span className="status-text">
            {integration.status === 'connected' ? 'Connesso' : 'Disconnesso'}
          </span>
        </div>
      </div>

      <div className="integration-features">
        <h4>Funzionalità:</h4>
        <div className="features-list">
          {integration.features.map((feature, index) => (
            <span key={index} className="feature-tag">
              {feature}
            </span>
          ))}
        </div>
      </div>

      <div className="integration-actions">
        {integration.status === 'connected' ? (
          <>
            <button 
              className="btn btn-outline"
              onClick={() => handleConnect(integration)}
            >
              Configura
            </button>
            <button 
              className="btn btn-secondary"
              onClick={() => handleDisconnect(integration.id)}
            >
              Disconnetti
            </button>
          </>
        ) : (
          <button 
            className="btn btn-primary"
            onClick={() => handleConnect(integration)}
          >
            Connetti
          </button>
        )}
      </div>
    </div>
  );

  // Render configuration form
  const renderConfigForm = () => {
    if (!activeIntegration) return null;

    return (
      <div className="crm-config-modal">
        <div className="config-modal-content">
          <div className="config-header">
            <h2>Configura {activeIntegration.name}</h2>
            <button 
              className="btn btn-outline btn-icon"
              onClick={() => setActiveIntegration(null)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="config-form">
            <div className="form-section">
              <h3>Credenziali di accesso</h3>
              
              <div className="form-group">
                <label className="form-label">API Key</label>
                <input
                  type="password"
                  className="form-input"
                  value={configForm.apiKey}
                  onChange={(e) => setConfigForm(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder="Inserisci la tua API Key"
                  required
                />
              </div>

              {activeIntegration.id !== 'custom-api' && (
                <div className="form-group">
                  <label className="form-label">API Secret</label>
                  <input
                    type="password"
                    className="form-input"
                    value={configForm.apiSecret}
                    onChange={(e) => setConfigForm(prev => ({ ...prev, apiSecret: e.target.value }))}
                    placeholder="Inserisci il tuo API Secret"
                    required
                  />
                </div>
              )}

              <div className="form-group">
                <label className="form-label">Instance URL</label>
                <input
                  type="url"
                  className="form-input"
                  value={configForm.instanceUrl}
                  onChange={(e) => setConfigForm(prev => ({ ...prev, instanceUrl: e.target.value }))}
                  placeholder="https://your-instance.salesforce.com"
                  required
                />
              </div>
            </div>

            <div className="form-section">
              <h3>Configurazione sincronizzazione</h3>
              
              <div className="form-group">
                <label className="form-label">Frequenza di sincronizzazione</label>
                <select 
                  className="form-select"
                  value={configForm.syncFrequency}
                  onChange={(e) => setConfigForm(prev => ({ ...prev, syncFrequency: e.target.value }))}
                >
                  <option value="realtime">Tempo reale</option>
                  <option value="hourly">Ogni ora</option>
                  <option value="daily">Giornaliera</option>
                  <option value="weekly">Settimanale</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">Webhook URL (opzionale)</label>
                <input
                  type="url"
                  className="form-input"
                  value={configForm.webhookUrl}
                  onChange={(e) => setConfigForm(prev => ({ ...prev, webhookUrl: e.target.value }))}
                  placeholder="https://your-app.com/webhook"
                />
              </div>
            </div>

            <div className="form-section">
              <h3>Mappatura dati</h3>
              <p className="form-description">
                Configura come i dati del CRM vengono mappati nel sistema iCodium
              </p>
              
              <div className="mapping-grid">
                <div className="mapping-item">
                  <label>Campo Lead</label>
                  <select className="form-select">
                    <option>Nome</option>
                    <option>Email</option>
                    <option>Telefono</option>
                    <option>Azienda</option>
                  </select>
                </div>
                <div className="mapping-item">
                  <label>Campo Opportunità</label>
                  <select className="form-select">
                    <option>Valore</option>
                    <option>Fase</option>
                    <option>Data chiusura</option>
                    <option>Probabilità</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="form-actions">
              <button type="button" className="btn btn-secondary" onClick={() => setActiveIntegration(null)}>
                Annulla
              </button>
              <button type="submit" className="btn btn-primary">
                Salva configurazione
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div className="crm-integrations-container">
      <div className="crm-integrations-header">
        <button className="btn btn-secondary" onClick={onBack}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Indietro
        </button>
        <div>
          <h1>Integrazioni CRM</h1>
          <p>Connetti il tuo sistema CRM per sincronizzare dati e automatizzare i processi</p>
        </div>
      </div>

      <div className="crm-integrations-content">
        <div className="integrations-stats">
          <div className="stat-card">
            <div className="stat-value">{integrations.filter(i => i.status === 'connected').length}</div>
            <div className="stat-label">Integrazioni attive</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{integrations.length}</div>
            <div className="stat-label">Integrazioni disponibili</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">24/7</div>
            <div className="stat-label">Sincronizzazione</div>
          </div>
        </div>

        <div className="integrations-grid">
          {integrations.map(integration => renderIntegrationCard(integration))}
        </div>
      </div>

      {renderConfigForm()}
    </div>
  );
};

export default CRMIntegrations;
