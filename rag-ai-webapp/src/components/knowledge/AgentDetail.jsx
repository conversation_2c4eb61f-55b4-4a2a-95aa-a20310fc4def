import React, { useState } from 'react';
import AgentConfig from './AgentConfig';
import AgentTester from './AgentTester';
import AgentMetrics from './AgentMetrics';
import KnowledgeSources from './KnowledgeSources';
import AddKnowledge from './AddKnowledge';
import '../../styles/agent-detail.css';

const AgentDetail = ({ agent, onAddKnowledge, onCreateSubAgent, onBack }) => {
  const [activeTab, setActiveTab] = useState('knowledge');
  const [agentConfig, setAgentConfig] = useState(agent.config || {});
  const [showAddKnowledge, setShowAddKnowledge] = useState(false);

  // Handle saving agent configuration
  const handleSaveConfig = (config) => {
    setAgentConfig(config);
    // In a real app, this would save to the backend
    console.log('Saved configuration:', config);
    alert('Configurazione salvata con successo!');
  };

  // Function to get source icon based on type
  const getSourceIcon = (type) => {
    switch(type) {
      case 'pdf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'webpage':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
            <polyline points="13 2 13 9 20 9"></polyline>
          </svg>
        );
    }
  };

  return (
    <div className="knowledge-content">
      <div className="knowledge-header">
        <button className="btn btn-secondary" onClick={onBack}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Indietro
        </button>
        <div className="header-actions">
          <button className="btn btn-secondary" onClick={onCreateSubAgent}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
              <line x1="12" y1="11" x2="12" y2="17"></line>
              <line x1="9" y1="14" x2="15" y2="14"></line>
            </svg>
            Crea sottoagente
          </button>
          <button className="btn btn-primary" onClick={onAddKnowledge}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Aggiungi conoscenza
          </button>
        </div>
      </div>

      <div className="agent-detail">
        <div className="agent-detail-header">
          <div className="agent-detail-avatar">
            {agent.avatar ? (
              <img src={agent.avatar} alt={agent.name} />
            ) : (
              agent.name.charAt(0)
            )}
          </div>
          <div className="agent-detail-info">
            <div className="agent-detail-name">{agent.name}</div>
            <div className="agent-detail-description">{agent.description}</div>
            <div className="agent-detail-stats">
              <div className="agent-detail-stat">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                  <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                </svg>
                {agent.sources.length} fonti di conoscenza
              </div>
              <div className="agent-detail-stat">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                Creato il {new Date().toLocaleDateString()}
              </div>
              <div className="agent-detail-stat">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                0 conversazioni
              </div>
            </div>
          </div>
        </div>

        <div className="knowledge-tabs">
          <div
            className={`knowledge-tab ${activeTab === 'knowledge' ? 'active' : ''}`}
            onClick={() => setActiveTab('knowledge')}
          >
            Fonti di conoscenza
          </div>
          <div
            className={`knowledge-tab ${activeTab === 'subagents' ? 'active' : ''}`}
            onClick={() => setActiveTab('subagents')}
          >
            Sottoagenti
          </div>
          <div
            className={`knowledge-tab ${activeTab === 'test' ? 'active' : ''}`}
            onClick={() => setActiveTab('test')}
          >
            Test
          </div>
          <div
            className={`knowledge-tab ${activeTab === 'metrics' ? 'active' : ''}`}
            onClick={() => setActiveTab('metrics')}
          >
            Metriche
          </div>
          <div
            className={`knowledge-tab ${activeTab === 'config' ? 'active' : ''}`}
            onClick={() => setActiveTab('config')}
          >
            Configurazione
          </div>
          <div
            className={`knowledge-tab ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            Impostazioni
          </div>
        </div>

        <div className="agent-detail-content">
          {activeTab === 'knowledge' && (
            <div className="agent-detail-section">
              {showAddKnowledge ? (
                <AddKnowledge
                  agent={agent}
                  onSave={(sourceData) => {
                    setShowAddKnowledge(false);
                    // In a real app, this would update the agent's knowledge sources
                    console.log('Knowledge source added:', sourceData);
                  }}
                  onCancel={() => setShowAddKnowledge(false)}
                />
              ) : (
                <KnowledgeSources
                  agent={agent}
                  onAddKnowledge={() => setShowAddKnowledge(true)}
                />
              )}
            </div>
          )}

          {activeTab === 'subagents' && (
            <div className="agent-detail-section">
              <div className="agent-detail-section-title">Sottoagenti</div>

              {agent.subAgents && agent.subAgents.length > 0 ? (
                <div className="agent-list">
                  {agent.subAgents.map(subAgent => (
                    <div key={subAgent.id} className="agent-list-item sub-agent-item">
                      <div className="agent-list-avatar">
                        {subAgent.avatar ? (
                          <img src={subAgent.avatar} alt={subAgent.name} />
                        ) : (
                          subAgent.name.charAt(0)
                        )}
                      </div>
                      <div className="agent-list-info">
                        <div className="agent-list-name">{subAgent.name}</div>
                        <div className="agent-list-description">{subAgent.description}</div>
                        <div className="agent-list-sources-count">
                          {subAgent.sources.length} fonti di conoscenza
                        </div>
                      </div>
                      <div className={`agent-list-status ${subAgent.status}`}>
                        {subAgent.status === 'active' ? 'Attivo' : 'Inattivo'}
                      </div>
                    </div>
                  ))}

                  <button className="btn btn-primary" onClick={onCreateSubAgent} style={{ marginTop: '15px' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Crea nuovo sottoagente
                  </button>
                </div>
              ) : (
                <div className="empty-state" style={{ padding: '30px 0' }}>
                  <div className="empty-state-icon" style={{ fontSize: '3rem' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <div className="empty-state-title">Nessun sottoagente creato</div>
                  <div className="empty-state-description">
                    Crea sottoagenti per specializzare le funzionalità di questo agente.
                    Ogni sottoagente può avere la propria conoscenza e configurazione.
                  </div>
                  <button className="btn btn-primary" onClick={onCreateSubAgent}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    Crea sottoagente
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'test' && (
            <div className="agent-detail-section">
              <AgentTester agent={agent} />
            </div>
          )}

          {activeTab === 'metrics' && (
            <div className="agent-detail-section">
              <AgentMetrics agent={agent} />
            </div>
          )}

          {activeTab === 'config' && (
            <div className="agent-detail-section">
              <AgentConfig agent={{...agent, config: agentConfig}} onSaveConfig={handleSaveConfig} />
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="agent-detail-section">
              <div className="agent-detail-section-title">Impostazioni</div>

              <div className="form-group">
                <label className="form-label">Nome dell'agente</label>
                <input type="text" className="form-input" value={agent.name} readOnly />
              </div>

              <div className="form-group">
                <label className="form-label">Descrizione</label>
                <textarea className="form-textarea" value={agent.description} readOnly></textarea>
              </div>

              <div className="form-group">
                <label className="form-label">Stato</label>
                <select className="form-select" value={agent.status}>
                  <option value="active">Attivo</option>
                  <option value="inactive">Inattivo</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">Modello di base</label>
                <div className="model-display">
                  <div className={`model-icon ${agent.model === 'icodium-agata-1' ? 'agata' : agent.model === 'icodium-scorpion-v2' ? 'scorpion' : 'maverick'}`}>
                    {agent.model === 'icodium-agata-1' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 16v-4"></path>
                        <path d="M12 8h.01"></path>
                      </svg>
                    ) : agent.model === 'icodium-scorpion-v2' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                        <path d="M2 17l10 5 10-5"></path>
                        <path d="M2 12l10 5 10-5"></path>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                        <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                        <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                        <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                      </svg>
                    )}
                  </div>
                  <div className="model-info">
                    <div className="model-name">
                      {agent.model === 'icodium-agata-1' ? 'iCodium Agata 1' :
                       agent.model === 'icodium-scorpion-v2' ? 'iCodium Scorpion V2' :
                       'iCodium Maverick'}
                    </div>
                    <div className="model-desc">
                      {agent.model === 'icodium-agata-1' ? 'Bilanciato e versatile' :
                       agent.model === 'icodium-scorpion-v2' ? 'Veloce ed efficiente' :
                       'Avanzato e potente'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="form-actions">
                <button className="btn btn-primary">Salva modifiche</button>
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="agent-detail-section">
              <div className="agent-detail-section-title">Analisi</div>

              <div className="empty-state" style={{ padding: '30px 0' }}>
                <div className="empty-state-icon" style={{ fontSize: '3rem' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path>
                    <path d="M22 12A10 10 0 0 0 12 2v10z"></path>
                  </svg>
                </div>
                <div className="empty-state-title">Nessun dato disponibile</div>
                <div className="empty-state-description">
                  I dati di analisi saranno disponibili dopo che l'agente avrà iniziato a essere utilizzato.
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentDetail;
