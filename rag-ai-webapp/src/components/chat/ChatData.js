// Dati di esempio per le chat
export const chatData = {
  'fav1': {
    id: 'fav1',
    name: '<PERSON><PERSON><PERSON> importante',
    messages: [
      { id: 1, content: '<PERSON>ia<PERSON>, ho bisogno di informazioni sul progetto RAG.', isAi: false, timestamp: '10:30' },
      { id: 2, content: 'Certo! Il progetto RAG (Retrieval-Augmented Generation) combina la potenza dei modelli linguistici con il recupero di informazioni specifiche. Cosa vorresti sapere in particolare?', isAi: true, timestamp: '10:31' },
      { id: 3, content: 'Quali sono i principali vantaggi rispetto ai sistemi tradizionali?', isAi: false, timestamp: '10:32' },
      { id: 4, content: 'I principali vantaggi dei sistemi RAG includono:\n\n1. Maggiore accuratezza: le risposte sono basate su informazioni recuperate da fonti affidabili\n2. Riduzione delle allucinazioni: il modello è ancorato a dati reali\n3. Aggiornabilità: la knowledge base può essere aggiornata senza riaddestramento del modello\n4. Trasparenza: le fonti delle informazioni possono essere citate e verificate\n5. Personalizzazione: può essere adattato a domini specifici', isAi: true, timestamp: '10:33' }
    ]
  },
  'fav2': {
    id: 'fav2',
    name: 'Ricerca AI',
    messages: [
      { id: 1, content: 'Quali sono le ultime novità nel campo dell\'intelligenza artificiale?', isAi: false, timestamp: '14:15' },
      { id: 2, content: 'Ci sono diverse innovazioni recenti nel campo dell\'AI:\n\n1. Modelli multimodali come GPT-4V che possono elaborare sia testo che immagini\n2. Progressi nei sistemi RAG (Retrieval-Augmented Generation)\n3. Miglioramenti nell\'apprendimento per rinforzo\n4. Nuovi approcci per ridurre il consumo energetico dei modelli AI\n5. Avanzamenti nell\'AI generativa per immagini, audio e video', isAi: true, timestamp: '14:16' }
    ]
  },
  '1-2': {
    id: '1-2',
    name: 'Supporto clienti',
    messages: [
      { id: 1, content: 'Come possiamo migliorare il nostro sistema di supporto clienti?', isAi: false, timestamp: '09:45' },
      { id: 2, content: 'Per migliorare il sistema di supporto clienti, potete considerare:\n\n1. Implementare un chatbot AI per rispondere alle domande frequenti\n2. Creare una knowledge base completa e facilmente navigabile\n3. Offrire supporto multicanale (email, chat, telefono)\n4. Raccogliere feedback dopo ogni interazione\n5. Analizzare i dati per identificare problemi ricorrenti', isAi: true, timestamp: '09:46' }
    ]
  },
  '1-3': {
    id: '1-3',
    name: 'Analisi dati Q2',
    messages: [
      { id: 1, content: 'Puoi aiutarmi ad analizzare i dati del secondo trimestre?', isAi: false, timestamp: '11:20' },
      { id: 2, content: 'Certo! Per analizzare i dati del secondo trimestre, possiamo seguire questi passaggi:\n\n1. Raccogliere tutti i dati rilevanti (vendite, marketing, costi, etc.)\n2. Pulire e normalizzare i dati\n3. Identificare le metriche chiave da analizzare\n4. Creare visualizzazioni appropriate (grafici, tabelle)\n5. Confrontare con i trimestri precedenti e gli obiettivi\n\nHai già i dati pronti per l\'analisi?', isAi: true, timestamp: '11:22' }
    ]
  },
  '1-4': {
    id: '1-4',
    name: 'Riunione marketing',
    messages: [
      { id: 1, content: 'Dobbiamo preparare la presentazione per la riunione di marketing di domani.', isAi: false, timestamp: '16:05' },
      { id: 2, content: 'Per la presentazione di marketing, suggerirei di includere:\n\n1. Risultati delle campagne recenti\n2. Analisi del comportamento dei clienti\n3. Confronto con i concorrenti\n4. Nuove opportunità di mercato\n5. Strategie proposte per il prossimo trimestre\n\nVuoi che ti aiuti a strutturare la presentazione?', isAi: true, timestamp: '16:07' }
    ]
  },
  '2-1': {
    id: '2-1',
    name: 'Idee vacanze',
    messages: [
      { id: 1, content: 'Sto cercando idee per le vacanze estive.', isAi: false, timestamp: '18:30' },
      { id: 2, content: 'Ecco alcune idee per le vacanze estive:\n\n1. Tour delle città europee (Roma, Parigi, Barcellona)\n2. Vacanza relax in una località balneare (Sardegna, Grecia, Croazia)\n3. Avventura in montagna (Dolomiti, Alpi Svizzere)\n4. Viaggio culturale in Asia (Giappone, Thailandia)\n5. Crociera nel Mediterraneo\n\nHai preferenze particolari o un budget specifico?', isAi: true, timestamp: '18:32' }
    ]
  },
  '2-2': {
    id: '2-2',
    name: 'Ricette',
    messages: [
      { id: 1, content: 'Cerco ricette veloci per la cena.', isAi: false, timestamp: '19:15' },
      { id: 2, content: 'Ecco alcune ricette veloci per la cena:\n\n1. Pasta al pomodoro e basilico (15 minuti)\n2. Insalata di quinoa con verdure (20 minuti)\n3. Frittata con verdure di stagione (10 minuti)\n4. Wrap di pollo e avocado (15 minuti)\n5. Zuppa di legumi (usando legumi precotti, 20 minuti)\n\nVuoi la ricetta dettagliata di una di queste?', isAi: true, timestamp: '19:17' }
    ]
  },
  '2-3': {
    id: '2-3',
    name: 'Appunti corso',
    messages: [
      { id: 1, content: 'Sto studiando i sistemi RAG e ho bisogno di appunti strutturati.', isAi: false, timestamp: '15:40' },
      { id: 2, content: 'Ecco degli appunti strutturati sui sistemi RAG:\n\n**1. Definizione**\n- RAG = Retrieval-Augmented Generation\n- Combina recupero di informazioni e generazione di testo\n\n**2. Componenti principali**\n- Knowledge Base (documenti, database)\n- Sistema di recupero (embedding, indici)\n- Modello linguistico (LLM)\n\n**3. Funzionamento**\n- Query dell\'utente → Recupero documenti rilevanti → Contestualizzazione → Generazione risposta\n\n**4. Vantaggi**\n- Riduzione delle allucinazioni\n- Informazioni aggiornate\n- Citazione delle fonti\n\n**5. Applicazioni**\n- Assistenti virtuali\n- Sistemi di supporto clienti\n- Ricerca documentale\n\nVuoi approfondire qualche sezione in particolare?', isAi: true, timestamp: '15:43' }
    ]
  }
};
