import { useState, useRef, useEffect } from 'react';
// import '../../styles/chat.css';

const ChatInterface = ({ onSendMessage, chatData }) => {
  const [messages, setMessages] = useState([
    {
      id: '1',
      content: 'Ciao! Come posso aiutarti oggi?',
      isAi: true,
      timestamp: '10:30',
      sources: []
    }
  ]);
  const [showWelcome, setShowWelcome] = useState(true);
  const [likedMessages, setLikedMessages] = useState({});
  const [dislikedMessages, setDislikedMessages] = useState({});
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef(null);

  // Update messages when chatData changes
  useEffect(() => {
    if (chatData && chatData.messages && chatData.messages.length > 0) {
      setMessages(chatData.messages);
      setShowWelcome(false);
    }
  }, [chatData]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = (message) => {
    // Hide welcome screen when user sends first message
    if (showWelcome) {
      setShowWelcome(false);
    }

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      content: message,
      isAi: false,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      sources: []
    };

    setMessages(prev => [...prev, userMessage]);

    // Notify parent component about the message (for reasoning panel)
    if (onSendMessage) {
      onSendMessage(message);
    }

    // Simulate AI response (in a real app, this would be an API call)
    setTimeout(() => {
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        content: "Basandomi sui documenti nel tuo Brain, ho trovato che queste informazioni sono correlate alla tua domanda. I dati mostrano che i sistemi RAG (Retrieval-Augmented Generation) combinano la potenza dei grandi modelli linguistici con il recupero di conoscenze specifiche per fornire risposte più accurate e contestuali.",
        isAi: true,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        sources: [
          {
            id: 's1',
            title: 'Introduzione ai sistemi RAG',
            type: 'pdf',
            url: 'https://example.com/rag-intro.pdf',
            relevance: 95
          },
          {
            id: 's2',
            title: 'Costruire soluzioni AI aziendali',
            type: 'webpage',
            url: 'https://example.com/enterprise-ai',
            relevance: 82
          },
          {
            id: 's3',
            title: 'Guida all\'integrazione della Knowledge Base',
            type: 'text',
            relevance: 68
          }
        ]
      };

      setMessages(prev => [...prev, aiMessage]);
    }, 1000);
  };

  const handleSuggestionClick = (suggestion) => {
    handleSendMessage(suggestion);
  };

  // Handle like/dislike actions
  const handleLike = (messageId) => {
    setLikedMessages(prev => {
      const newState = {...prev};
      if (newState[messageId]) {
        delete newState[messageId];
      } else {
        newState[messageId] = true;
        // Remove from disliked if it was disliked
        if (dislikedMessages[messageId]) {
          setDislikedMessages(prev => {
            const newDisliked = {...prev};
            delete newDisliked[messageId];
            return newDisliked;
          });
        }
      }
      return newState;
    });
  };

  const handleDislike = (messageId) => {
    setDislikedMessages(prev => {
      const newState = {...prev};
      if (newState[messageId]) {
        delete newState[messageId];
      } else {
        newState[messageId] = true;
        // Remove from liked if it was liked
        if (likedMessages[messageId]) {
          setLikedMessages(prev => {
            const newLiked = {...prev};
            delete newLiked[messageId];
            return newLiked;
          });
        }
      }
      return newState;
    });
  };

  // Handle copy message
  const handleCopyMessage = (content) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        // You could show a toast notification here
        console.log('Messaggio copiato negli appunti');
      })
      .catch(err => {
        console.error('Errore durante la copia: ', err);
      });
  };

  // Handle report issue
  const handleReportIssue = (messageId) => {
    // In a real app, this would open a modal or form to report the issue
    console.log('Segnalazione problema per il messaggio ID:', messageId);
    alert('Grazie per la segnalazione. Il nostro team esaminerà il messaggio.');
  };

  // Handle file attachment
  const handleAttachFile = () => {
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt';
    fileInput.style.display = 'none';

    // Add the file input to the document
    document.body.appendChild(fileInput);

    // Add event listener for when a file is selected
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        // In a real app, you would upload the file to a server
        // For now, we'll just show a message with the file name
        const fileInfo = `${file.name} (${(file.size / 1024).toFixed(2)} KB)`;
        handleSendMessage(`Ho allegato un file: ${fileInfo}`);
        console.log('File allegato:', file);
      }

      // Remove the file input from the document
      document.body.removeChild(fileInput);
    });

    // Trigger the file input click event
    fileInput.click();
  };

  // Handle audio recording
  const handleRecordAudio = () => {
    // If already recording, stop recording
    if (isRecording) {
      setIsRecording(false);
      handleSendMessage('Ho registrato un messaggio audio.');
      return;
    }

    // Check if the browser supports the MediaRecorder API
    if (!navigator.mediaDevices || !window.MediaRecorder) {
      alert('Il tuo browser non supporta la registrazione audio. Prova con Chrome o Firefox.');
      return;
    }

    // Ask for permission to use the microphone
    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        // Set recording state to true
        setIsRecording(true);

        // In a real app, you would implement the recording functionality
        console.log('Registrazione audio avviata');

        // In a real app, you would keep the stream reference and stop it when the user stops recording
        // For now, we'll just simulate a recording with a timeout
        setTimeout(() => {
          if (isRecording) {
            setIsRecording(false);
            stream.getTracks().forEach(track => track.stop());
            handleSendMessage('Ho registrato un messaggio audio.');
          }
        }, 10000); // Auto-stop after 10 seconds for demo purposes
      })
      .catch(err => {
        console.error('Errore durante l\'accesso al microfono:', err);
        alert('Non è stato possibile accedere al microfono. Verifica le autorizzazioni del browser.');
      });
  };

  // Handle document upload
  const handleUploadDocument = () => {
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.pdf,.doc,.docx,.txt';
    fileInput.style.display = 'none';

    // Add the file input to the document
    document.body.appendChild(fileInput);

    // Add event listener for when a file is selected
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        // In a real app, you would upload the document to a server and process it
        // For now, we'll just show a message with the document name
        const fileInfo = `${file.name} (${(file.size / 1024).toFixed(2)} KB)`;
        handleSendMessage(`Ho caricato un documento: ${fileInfo}. In un'app reale, questo documento verrebbe analizzato dal sistema RAG.`);
        console.log('Documento caricato:', file);

        // Simulate a RAG response after a short delay
        setTimeout(() => {
          const aiResponse = {
            id: Date.now().toString(),
            content: `Ho analizzato il documento "${file.name}". Ecco un riassunto dei punti principali:\n\n1. Il documento contiene informazioni su sistemi RAG\n2. Sono presenti riferimenti a tecniche di embedding\n3. Ci sono esempi di implementazione con diversi LLM\n\nPuoi farmi domande specifiche sul contenuto del documento.`,
            isAi: true,
            timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
            sources: [
              {
                title: file.name,
                url: '#',
                relevance: 95
              }
            ]
          };

          setMessages(prev => [...prev, aiResponse]);
        }, 2000);
      }

      // Remove the file input from the document
      document.body.removeChild(fileInput);
    });

    // Trigger the file input click event
    fileInput.click();
  };

  // Handle search in knowledge base
  const handleSearchKnowledgeBase = (query) => {
    // In a real app, this would search the knowledge base
    // For now, we'll just simulate a search
    handleSendMessage(`Cerco nella knowledge base: "${query || 'informazioni sui sistemi RAG'}"`);

    // Simulate a search response after a short delay
    setTimeout(() => {
      const aiResponse = {
        id: Date.now().toString(),
        content: `Ho trovato diverse informazioni nella knowledge base:\n\n1. **Architettura RAG**: I sistemi RAG combinano retrieval e generation per migliorare l'accuratezza delle risposte\n2. **Embedding**: Le tecniche di embedding più efficaci per documenti tecnici includono modelli specializzati\n3. **Chunking**: Le strategie di chunking semantico migliorano significativamente la qualità del retrieval\n\nVuoi approfondire uno di questi argomenti?`,
        isAi: true,
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
        sources: [
          {
            title: 'RAG Architecture Guide',
            url: '#',
            relevance: 92
          },
          {
            title: 'Embedding Techniques for Technical Documents',
            url: '#',
            relevance: 87
          },
          {
            title: 'Semantic Chunking Strategies',
            url: '#',
            relevance: 85
          }
        ]
      };

      setMessages(prev => [...prev, aiResponse]);
    }, 1500);
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <div className="chat-header-left">
          <div className="chat-agent-info">
            <div className="chat-agent-avatar">
              <span>🤖</span>
            </div>
            <div className="chat-agent-details">
              <div className="chat-agent-name">iCodium Assistant</div>
              <div className="chat-agent-status">Online</div>
            </div>
          </div>
        </div>
        <div className="chat-header-actions">
          <button className="chat-action-btn" title="Nuova conversazione">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 5v14M5 12h14"></path>
            </svg>
            <span>Nuova</span>
          </button>
          <button className="chat-action-btn" title="Pulisci chat">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            </svg>
            <span>Pulisci</span>
          </button>
        </div>
      </div>

      {showWelcome ? (
        <div className="chat-welcome">
          <div className="chat-welcome-content">
            <div className="chat-welcome-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </div>
            <h2 className="chat-welcome-title">Benvenuto in iCodium Assistant</h2>
            <p className="chat-welcome-text">
              Fai domande sui tuoi documenti, ottieni informazioni dai tuoi dati o esplora nuove idee con l'assistenza basata sull'intelligenza artificiale.
            </p>
          </div>

          <div className="chat-suggestions-wrapper">
            <h3 className="chat-suggestions-title">Prova a chiedere</h3>
            <div className="chat-suggestion-grid">
              <div className="chat-suggestion" onClick={() => handleSuggestionClick("Spiega l'architettura di un sistema RAG avanzato con embedding densi e retrieval multi-stage")}>
                <div className="chat-suggestion-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                  </svg>
                </div>
                <div className="chat-suggestion-content">
                  <div className="chat-suggestion-title">Architettura RAG avanzata</div>
                  <div className="chat-suggestion-description">Embedding densi, retrieval multi-stage e ottimizzazione delle query</div>
                </div>
              </div>

              <div className="chat-suggestion" onClick={() => handleSuggestionClick("Come implementare un sistema di chunking adattivo per documenti lunghi con sovrapposizione semantica?")}>
                <div className="chat-suggestion-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                </div>
                <div className="chat-suggestion-content">
                  <div className="chat-suggestion-title">Chunking adattivo</div>
                  <div className="chat-suggestion-description">Tecniche avanzate di segmentazione per documenti lunghi con sovrapposizione semantica</div>
                </div>
              </div>

              <div className="chat-suggestion" onClick={() => handleSuggestionClick("Quali sono le tecniche di re-ranking più efficaci per migliorare la precisione in un sistema RAG?")}>
                <div className="chat-suggestion-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="4" y1="21" x2="4" y2="14"></line>
                    <line x1="4" y1="10" x2="4" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="12"></line>
                    <line x1="12" y1="8" x2="12" y2="3"></line>
                    <line x1="20" y1="21" x2="20" y2="16"></line>
                    <line x1="20" y1="12" x2="20" y2="3"></line>
                    <line x1="1" y1="14" x2="7" y2="14"></line>
                    <line x1="9" y1="8" x2="15" y2="8"></line>
                    <line x1="17" y1="16" x2="23" y2="16"></line>
                  </svg>
                </div>
                <div className="chat-suggestion-content">
                  <div className="chat-suggestion-title">Tecniche di re-ranking</div>
                  <div className="chat-suggestion-description">Metodi avanzati per migliorare la precisione del retrieval nei sistemi RAG</div>
                </div>
              </div>

              <div className="chat-suggestion" onClick={() => handleSuggestionClick("Come implementare un sistema di valutazione automatica per misurare la qualità delle risposte RAG?")}>
                <div className="chat-suggestion-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <div className="chat-suggestion-content">
                  <div className="chat-suggestion-title">Valutazione automatica RAG</div>
                  <div className="chat-suggestion-description">Metriche e framework per misurare la qualità, rilevanza e accuratezza delle risposte</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="chat-messages">
          {messages.map(message => (
            <div key={message.id} className={`chat-message ${message.isAi ? 'ai' : 'user'}`}>
              {message.isAi && (
                <div className="chat-message-avatar ai">
                  <span>🤖</span>
                </div>
              )}
              <div className="chat-message-bubble">
                <div className="chat-message-content">{message.content}</div>
                <div className="chat-message-footer">
                  <div className="chat-message-time">{message.timestamp}</div>
                  {message.isAi && (
                    <div className="chat-message-actions">
                      <button
                        className={`chat-action-btn ${likedMessages[message.id] ? 'active' : ''}`}
                        title="Mi piace"
                        onClick={() => handleLike(message.id)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                        </svg>
                      </button>
                      <button
                        className={`chat-action-btn ${dislikedMessages[message.id] ? 'active' : ''}`}
                        title="Non mi piace"
                        onClick={() => handleDislike(message.id)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>
                        </svg>
                      </button>
                      <button
                        className="chat-action-btn"
                        title="Copia risposta"
                        onClick={() => handleCopyMessage(message.content)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                        </svg>
                      </button>
                      <button
                        className="chat-action-btn"
                        title="Segnala problema"
                        onClick={() => handleReportIssue(message.id)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                          <line x1="12" y1="9" x2="12" y2="13"></line>
                          <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              </div>
              {!message.isAi && (
                <div className="chat-message-avatar user">
                  <span>👤</span>
                </div>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      )}

      <div className="chat-input-area">
        <div className="chat-input-tools">
          <button className="chat-tool-btn" title="Carica documento" onClick={handleUploadDocument}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="12" y1="18" x2="12" y2="12"></line>
              <line x1="9" y1="15" x2="15" y2="15"></line>
            </svg>
            <span className="tool-label">Documento</span>
          </button>
          <button className={`chat-tool-btn ${isRecording ? 'recording' : ''}`} title={isRecording ? 'Ferma registrazione' : 'Registra audio'} onClick={handleRecordAudio}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
            <span className="tool-label">Audio</span>
          </button>
          <button className="chat-tool-btn" title="Cerca nella knowledge base" onClick={() => handleSearchKnowledgeBase()}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
            <span className="tool-label">Cerca</span>
          </button>
        </div>
        <div className="chat-input-container">
          <textarea
            className="chat-input"
            placeholder="Scrivi un messaggio..."
            rows="1"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const message = e.target.value.trim();
                if (message) {
                  handleSendMessage(message);
                  e.target.value = '';
                }
              }
            }}
          ></textarea>
          <button
            className="chat-send-btn"
            title="Invia messaggio"
            onClick={(e) => {
              const textarea = e.target.closest('.chat-input-area').querySelector('.chat-input');
              const message = textarea.value.trim();
              if (message) {
                handleSendMessage(message);
                textarea.value = '';
              }
            }}
          >
            <span className="send-icon">&#10148;</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
