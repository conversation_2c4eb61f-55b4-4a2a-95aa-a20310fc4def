import { useState, useRef, useEffect } from 'react';
import '../../styles/reasoning.css';

const ReasoningPanel = () => {
  // Stato per l'espansione del pannello delle fonti
  const [sourcesExpanded, setSourcesExpanded] = useState(false);

  // Riferimento al contenitore del flusso di pensiero
  const thoughtFlowRef = useRef(null);

  // Riferimento all'elemento di spaziatura finale
  const endSpacerRef = useRef(null);

  // Dati di esempio per gli agenti principali
  const mainAgents = [
    {
      id: 'a1',
      name: 'RAG Assistant',
      role: 'Assistente principale',
      avatar: '🤖',
      color: '#7122ff'
    },
    {
      id: 'a2',
      name: 'Knowledge Agent',
      role: 'Gestione della conoscenza',
      avatar: '📖',
      color: '#4a6cf7'
    },
    {
      id: 'a3',
      name: 'Integration Agent',
      role: 'Integrazione con sistemi esterni',
      avatar: '🔗',
      color: '#ff6b6b'
    }
  ];

  // Dati di esempio per i passaggi di ragionamento
  const [reasoningSteps] = useState([
    {
      id: 1,
      title: '<PERSON><PERSON><PERSON> della domanda',
      time: '10:30:15',
      content: 'La domanda riguarda i sistemi RAG (Retrieval-Augmented Generation). Devo identificare i concetti chiave e cercare informazioni pertinenti nella knowledge base.',
      agent: {
        id: 'a1',
        name: 'RAG Assistant',
        avatar: '🤖',
        color: '#7122ff'
      },
      subAgent: {
        id: 'a1-1',
        name: 'Analizzatore',
        role: 'Analisi semantica',
        avatar: '🧠',
        color: '#9966ff'
      },
      tool: {
        id: 't1',
        name: 'Text Analyzer',
        description: 'Analisi semantica del testo',
        icon: '🔎'
      }
    },
    {
      id: 2,
      title: 'Ricerca documenti',
      time: '10:30:16',
      content: 'Cerco documenti relativi a "RAG", "Retrieval-Augmented Generation", "AI systems", "knowledge retrieval".\n\nDocumenti trovati:\n- "Introduzione ai sistemi RAG" (rilevanza: 95%)\n- "Costruire soluzioni AI aziendali" (rilevanza: 82%)\n- "Guida all\'integrazione della Knowledge Base" (rilevanza: 68%)',
      agent: {
        id: 'a2',
        name: 'Knowledge Agent',
        avatar: '📖',
        color: '#4a6cf7'
      },
      subAgent: {
        id: 'a2-1',
        name: 'Ricercatore',
        role: 'Retrieval di documenti',
        avatar: '🔍',
        color: '#6a8cff'
      },
      tool: {
        id: 't2',
        name: 'Document Retriever',
        description: 'Ricerca di documenti nella knowledge base',
        icon: '🗃️'
      }
    },
    {
      id: 3,
      title: 'Estrazione informazioni',
      time: '10:30:17',
      content: 'Dal documento "Introduzione ai sistemi RAG":\n```\nI sistemi RAG (Retrieval-Augmented Generation) combinano la potenza dei grandi modelli linguistici con il recupero di conoscenze specifiche per fornire risposte più accurate e contestuali.\n```\n\nDal documento "Costruire soluzioni AI aziendali":\n```\nL\'integrazione di sistemi RAG nelle soluzioni aziendali permette di sfruttare i dati proprietari mantenendo il controllo sulla fonte delle informazioni.\n```',
      agent: {
        id: 'a2',
        name: 'Knowledge Agent',
        avatar: '📖',
        color: '#4a6cf7'
      },
      subAgent: {
        id: 'a2-2',
        name: 'Estrattore',
        role: 'Estrazione di informazioni',
        avatar: '📑',
        color: '#6a8cff'
      },
      tool: {
        id: 't3',
        name: 'Content Extractor',
        description: 'Estrazione di informazioni dai documenti',
        icon: '✂️'
      }
    },
    {
      id: 4,
      title: 'Formulazione risposta',
      time: '10:30:18',
      content: 'Basandomi sui documenti recuperati, formulo una risposta che spiega cosa sono i sistemi RAG, come funzionano e quali vantaggi offrono, citando le fonti appropriate.',
      agent: {
        id: 'a1',
        name: 'RAG Assistant',
        avatar: '🤖',
        color: '#7122ff'
      },
      subAgent: {
        id: 'a1-2',
        name: 'Sintetizzatore',
        role: 'Generazione di risposte',
        avatar: '✍️',
        color: '#9966ff'
      },
      tool: {
        id: 't4',
        name: 'Response Generator',
        description: 'Generazione di risposte basate sulle informazioni estratte',
        icon: '💬'
      }
    },
  ]);

  // Dati di esempio per le fonti utilizzate
  const [sources] = useState([
    {
      id: 's1',
      title: 'Introduzione ai sistemi RAG',
      type: 'pdf',
      url: 'https://example.com/rag-intro.pdf',
      relevance: 95
    },
    {
      id: 's2',
      title: 'Costruire soluzioni AI aziendali',
      type: 'webpage',
      url: 'https://example.com/enterprise-ai',
      relevance: 82
    },
    {
      id: 's3',
      title: 'Guida all\'integrazione della Knowledge Base',
      type: 'text',
      relevance: 68
    },
    {
      id: 's4',
      title: 'Architetture avanzate per sistemi RAG',
      type: 'pdf',
      url: 'https://example.com/advanced-rag.pdf',
      relevance: 75
    },
    {
      id: 's5',
      title: 'Ottimizzazione delle query in sistemi di retrieval',
      type: 'webpage',
      url: 'https://example.com/query-optimization',
      relevance: 70
    },
    {
      id: 's6',
      title: 'Embedding semantici per la ricerca documentale',
      type: 'pdf',
      url: 'https://example.com/semantic-embeddings.pdf',
      relevance: 65
    },
    {
      id: 's7',
      title: 'Valutazione delle performance nei sistemi RAG',
      type: 'text',
      relevance: 60
    },
    {
      id: 's8',
      title: 'Integrazione di fonti eterogenee in sistemi RAG',
      type: 'webpage',
      url: 'https://example.com/heterogeneous-sources',
      relevance: 55
    },
    {
      id: 's9',
      title: 'Tecniche di chunking per documenti lunghi',
      type: 'pdf',
      url: 'https://example.com/chunking-techniques.pdf',
      relevance: 50
    },
    {
      id: 's10',
      title: 'Casi d\'uso aziendali per sistemi RAG',
      type: 'text',
      relevance: 45
    }
  ]);

  // Effetto per scrollare automaticamente all'ultimo elemento quando cambia il contenuto
  useEffect(() => {
    // Assicuriamoci che i riferimenti siano validi
    if (thoughtFlowRef.current && endSpacerRef.current) {
      // Calcoliamo la posizione dell'ultimo elemento
      const lastElement = thoughtFlowRef.current.querySelector('.rag-thought-step:last-child');
      if (lastElement) {
        // Scrolliamo fino all'ultimo elemento con un po' di spazio extra
        thoughtFlowRef.current.scrollTop = lastElement.offsetTop - 100;
      }
    }
  }, []); // Eseguiamo solo al montaggio del componente

  // Funzione per scrollare fino all'ultimo elemento
  const scrollToBottom = () => {
    if (thoughtFlowRef.current) {
      const lastElement = thoughtFlowRef.current.querySelector('.rag-thought-step:last-child');
      if (lastElement) {
        lastElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  // Funzione per ottenere l'icona in base al tipo di fonte
  const getSourceIcon = (type) => {
    switch(type) {
      case 'pdf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        );
      case 'webpage':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
            <polyline points="13 2 13 9 20 9"></polyline>
          </svg>
        );
    }
  };

  return (
    <div className="rag-reasoning-container">
      <div className="rag-reasoning-header">
        <div className="rag-reasoning-title">Ragionamento AI</div>
        <div className="rag-reasoning-controls">
          <button className="rag-reasoning-control-btn" title="Scorri fino all'ultimo elemento" onClick={scrollToBottom}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
          <button className="rag-reasoning-control-btn" title="Copia negli appunti">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
          <button className="rag-reasoning-control-btn" title="Esporta">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
              <polyline points="16 6 12 2 8 6"></polyline>
              <line x1="12" y1="2" x2="12" y2="15"></line>
            </svg>
          </button>
        </div>
      </div>

      <div className={`rag-reasoning-content ${sourcesExpanded ? 'sources-expanded' : ''}`}>
        <div className="rag-thought-flow" ref={thoughtFlowRef}>
          {reasoningSteps.map((step, index) => (
            <div key={step.id} className="rag-thought-step">
              <div className="rag-thought-number">{index + 1}</div>
              <div className="rag-thought-bubble">
                <div className="rag-thought-header">
                  <div className="rag-thought-title">{step.title}</div>
                  <div className="rag-thought-time">{step.time}</div>
                </div>
                <div className="rag-thought-body">{step.content}</div>
                <div className="rag-thought-agents">
                  <div className="rag-agent-info-box" style={{borderColor: step.agent.color}}>
                    <div className="rag-agent-avatar" style={{backgroundColor: step.agent.color + '20', color: step.agent.color}}>{step.agent.avatar}</div>
                    <div className="rag-agent-details">
                      <div className="rag-agent-name" style={{color: step.agent.color}}>{step.agent.name}</div>
                      <div className="rag-agent-tool">
                        <span className="rag-tool-icon">{step.tool.icon}</span>
                        <span className="rag-tool-name">{step.tool.name}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div className="rag-thought-end-spacer" ref={endSpacerRef}></div>
        </div>

        <div className={`rag-sources-panel ${sourcesExpanded ? 'expanded' : ''}`}>
          <div className="rag-sources-header">
            <div className="rag-sources-title">Fonti utilizzate</div>
            <button
              className="rag-sources-expand-btn"
              onClick={() => setSourcesExpanded(!sourcesExpanded)}
              title={sourcesExpanded ? "Riduci pannello" : "Espandi pannello"}
            >
              {sourcesExpanded ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="18 15 12 9 6 15"></polyline>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              )}
            </button>
          </div>
          <div className="rag-sources-list">
            {sources.map(source => (
              <div key={source.id} className="rag-source-item">
                <div className="rag-source-relevance-indicator" style={{opacity: source.relevance / 100}}></div>
                <div className="rag-source-icon">{getSourceIcon(source.type)}</div>
                <div className="rag-source-title">{source.title}</div>
                <div className="rag-source-relevance">{source.relevance}%</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReasoningPanel;
