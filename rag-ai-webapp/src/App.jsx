import { useState, useEffect } from 'react';
import Sidebar from './components/layout/Sidebar';
import TopBar from './components/layout/TopBar';
import ChatInterface from './components/chat/ChatInterface';
import ReasoningPanel from './components/reasoning/ReasoningPanel';
import KnowledgeBase from './components/knowledge/KnowledgeBase';
import IntegrationsSettings from './components/settings/IntegrationsSettings';
import { chatData } from './components/chat/ChatData';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const [activeSection, setActiveSection] = useState('chat');
  const [activeAgent, setActiveAgent] = useState('a1');
  const [activeChat, setActiveChat] = useState(null);
  const [currentChatData, setCurrentChatData] = useState(null);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  // Apply dark mode class to body
  useEffect(() => {
    if (darkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [darkMode]);

  // Load chat data when activeChat changes
  useEffect(() => {
    if (activeChat && chatData[activeChat]) {
      setCurrentChatData(chatData[activeChat]);
    } else {
      setCurrentChatData(null);
    }
  }, [activeChat]);

  // Handle message sent from chat interface
  const handleSendMessage = (message) => {
    console.log('Message sent:', message);
    // In a real app, this would trigger the reasoning panel to update

    // If we have an active chat, add the message to it
    if (currentChatData) {
      const newMessage = {
        id: currentChatData.messages.length + 1,
        content: message,
        isAi: false,
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };

      // Create AI response
      const aiResponse = {
        id: currentChatData.messages.length + 2,
        content: "Questa è una risposta di esempio. In un'applicazione reale, qui verrebbe generata una risposta basata sul tuo messaggio utilizzando il sistema RAG.",
        isAi: true,
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };

      // Update the chat data
      const updatedChatData = {
        ...currentChatData,
        messages: [...currentChatData.messages, newMessage, aiResponse]
      };

      setCurrentChatData(updatedChatData);
    }
  };

  // Render content based on active section
  const renderContent = () => {
    switch(activeSection) {
      case 'chat':
        return (
          <div className="rag-interface">
            <div className="rag-chat-panel">
              <ChatInterface onSendMessage={handleSendMessage} chatData={currentChatData} />
            </div>
            <div className="rag-reasoning-panel">
              <ReasoningPanel />
            </div>
          </div>
        );
      case 'brain':
        return <KnowledgeBase />;
      case 'settings':
        return <IntegrationsSettings />;
      default:
        return (
          <div className="rag-interface">
            <div className="rag-chat-panel">
              <ChatInterface onSendMessage={handleSendMessage} chatData={currentChatData} />
            </div>
            <div className="rag-reasoning-panel">
              <ReasoningPanel />
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`app ${darkMode ? 'dark-theme' : ''}`} data-theme={darkMode ? 'dark' : 'light'}>
      <TopBar
        darkMode={darkMode}
        toggleDarkMode={toggleDarkMode}
        activeAgent={activeAgent}
        setActiveAgent={setActiveAgent}
        activeSection={activeSection}
        setActiveSection={setActiveSection}
      />
      <div className="main-layout">
        <Sidebar
          activeSection={activeSection}
          setActiveSection={setActiveSection}
          activeChat={activeChat}
          setActiveChat={setActiveChat}
        />
        <div className="content-area">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default App;
