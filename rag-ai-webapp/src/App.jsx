function App() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#0e66b0' }}>🚀 iCodium Test</h1>
      <p>Se vedi questo messaggio, React funziona!</p>
      <div style={{
        background: '#f0f7ff',
        padding: '15px',
        borderRadius: '8px',
        marginTop: '20px',
        border: '1px solid #0e66b0'
      }}>
        <h3>✅ Test di caricamento riuscito</h3>
        <p>L'applicazione React si sta caricando correttamente.</p>
      </div>
    </div>
  );

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  // Apply dark mode class to body
  useEffect(() => {
    if (darkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }, [darkMode]);

  // Load chat data when activeChat changes
  useEffect(() => {
    if (activeChat && chatData[activeChat]) {
      setCurrentChatData(chatData[activeChat]);
    } else {
      setCurrentChatData(null);
    }
  }, [activeChat]);

  // Handle message sent from chat interface
  const handleSendMessage = (message) => {
    console.log('Message sent:', message);
    // In a real app, this would trigger the reasoning panel to update

    // If we have an active chat, add the message to it
    if (currentChatData) {
      const newMessage = {
        id: currentChatData.messages.length + 1,
        content: message,
        isAi: false,
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };

      // Create AI response
      const aiResponse = {
        id: currentChatData.messages.length + 2,
        content: "Questa è una risposta di esempio. In un'applicazione reale, qui verrebbe generata una risposta basata sul tuo messaggio utilizzando il sistema RAG.",
        isAi: true,
        timestamp: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
      };

      // Update the chat data
      const updatedChatData = {
        ...currentChatData,
        messages: [...currentChatData.messages, newMessage, aiResponse]
      };

      setCurrentChatData(updatedChatData);
    }
  };

  // Render content based on active section
  const renderContent = () => {
    switch(activeSection) {
      case 'chat':
        return (
          <div className="flex" style={{ height: 'calc(100vh - var(--header-height))' }}>
            <div className="chat-container">
              <ChatInterface onSendMessage={handleSendMessage} chatData={currentChatData} />
            </div>
            <div className="reasoning-panel">
              <ReasoningPanel />
            </div>
          </div>
        );
      case 'brain':
        return <KnowledgeBase />;
      case 'settings':
        return <IntegrationsSettings />;
      default:
        return (
          <div className="flex" style={{ height: 'calc(100vh - var(--header-height))' }}>
            <div className="chat-container">
              <ChatInterface onSendMessage={handleSendMessage} chatData={currentChatData} />
            </div>
            <div className="reasoning-panel">
              <ReasoningPanel />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="app">
      <h1>iCodium App</h1>
      <p>L'applicazione si sta caricando...</p>
      <button className="btn" onClick={toggleDarkMode}>
        {darkMode ? 'Light Mode' : 'Dark Mode'}
      </button>
      <p>Sezione attiva: {activeSection}</p>
    </div>
  );
}

export default App;
