/**
 * Stili per il pannello di ragionamento
 */

/* Pannello principale */
.reasoning-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  flex: 0.3; /* 30% dello spazio disponibile */
  overflow: hidden;
  padding: 0;
}

/* Header del pannello */
.reasoning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-background);
}

.reasoning-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
}

.reasoning-controls {
  display: flex;
  gap: 10px;
}

.reasoning-control-btn {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 5px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.reasoning-control-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

/* Contenitore principale con layout a due sezioni */
.reasoning-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px); /* Altezza totale meno header */
  overflow: hidden;
  padding: 0;
  justify-content: space-between; /* Distribuisce lo spazio tra le sezioni */
}

/* Stili comuni per le sezioni */
.reasoning-section {
  border-radius: var(--border-radius);
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  margin: 15px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.reasoning-section-header {
  font-weight: 600;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.02);
}

/* Sezione dei passaggi di ragionamento */
.reasoning-steps-section {
  flex: 0 0 auto;
  height: 60%; /* Occupa il 60% dell'altezza disponibile */
  margin-bottom: 0;
}

.reasoning-steps-container {
  padding: 15px;
  overflow-y: auto;
  height: calc(100% - 50px); /* Altezza totale meno header */
}

.reasoning-step {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
}

.reasoning-step:last-child {
  margin-bottom: 0;
}

.reasoning-step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.reasoning-step-title {
  font-weight: 600;
  color: var(--primary-color);
}

.reasoning-step-time {
  font-size: 0.8rem;
  color: var(--text-light);
}

.reasoning-step-content {
  color: var(--text-color);
  white-space: pre-wrap;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
}

.reasoning-step-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
}

.reasoning-step-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin: 10px 0;
}

/* Sezione delle fonti utilizzate */
.reasoning-sources-section {
  flex: 0 0 auto;
  height: 35%; /* Occupa il 35% dell'altezza disponibile */
  display: flex;
  flex-direction: column;
  min-height: 200px; /* Altezza minima garantita */
}

.reasoning-sources-container {
  padding: 15px;
  overflow-y: auto;
  height: calc(100% - 50px); /* Altezza totale meno header */
}

.reasoning-source-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: var(--border-radius);
  margin-bottom: 10px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.reasoning-source-item:last-child {
  margin-bottom: 0;
}

.reasoning-source-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.reasoning-source-icon {
  margin-right: 12px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.reasoning-source-title {
  font-weight: 500;
  flex: 1;
}

.reasoning-source-relevance {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 10px;
  background-color: rgba(113, 34, 255, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(113, 34, 255, 0.2);
  font-weight: 600;
}

/* Stili per i messaggi di stato vuoto */
.reasoning-empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-light);
  text-align: center;
  padding: 20px;
  font-style: italic;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius);
}

/* Assicuriamo che ci sia un po' di spazio tra le sezioni */
.reasoning-steps-section {
  margin-bottom: 10px;
}
