/* === REASONING PANEL === */
.reasoning-panel {
  width: 30%;
  background-color: var(--surface-primary);
  border-left: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.reasoning-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
}

.reasoning-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.reasoning-title svg {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--secondary);
}

.reasoning-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.reasoning-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
}

/* === REASONING STEPS === */
.reasoning-steps {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.reasoning-step {
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  transition: all var(--transition-fast);
}

.reasoning-step:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.reasoning-step-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.reasoning-step-number {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-full);
  background-color: var(--primary);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.reasoning-step-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.reasoning-step-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--surface-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.reasoning-step-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--space-3) 0;
}

.reasoning-step-tools {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.reasoning-tool {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  background-color: var(--tertiary-50);
  color: var(--tertiary-700);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.reasoning-tool svg {
  width: 0.875rem;
  height: 0.875rem;
}

.reasoning-step-agents {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.reasoning-agent {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  transition: all var(--transition-fast);
}

.reasoning-agent:hover {
  border-color: var(--primary-200);
  background-color: var(--primary-50);
}

.reasoning-agent-avatar {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: var(--radius-sm);
  background: linear-gradient(135deg, var(--secondary), var(--tertiary));
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-weight: var(--font-weight-semibold);
}

.reasoning-agent-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* === SOURCES SECTION === */
.sources-section {
  border-top: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
}

.sources-header {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sources-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.sources-title svg {
  width: 1rem;
  height: 1rem;
  color: var(--primary);
}

.sources-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sources-toggle:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.sources-toggle svg {
  width: 1rem;
  height: 1rem;
  transition: transform var(--transition-fast);
}

.sources-toggle.expanded svg {
  transform: rotate(180deg);
}

.sources-content {
  max-height: 200px;
  overflow-y: auto;
  padding: var(--space-4) var(--space-6);
}

.sources-content.expanded {
  max-height: none;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.source-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.source-item:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.source-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-sm);
  background-color: var(--primary-100);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.source-icon svg {
  width: 0.875rem;
  height: 0.875rem;
}

.source-content {
  flex: 1;
  min-width: 0;
}

.source-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--line-height-tight);
}

.source-excerpt {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

.source-confidence {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--success);
  background-color: var(--success);
  background-color: rgba(16, 185, 129, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

/* === EMPTY STATES === */
.reasoning-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: var(--space-12);
}

.reasoning-empty-icon {
  margin-bottom: var(--space-4);
  color: var(--text-tertiary);
  opacity: 0.6;
}

.reasoning-empty-icon svg {
  width: 3rem;
  height: 3rem;
}

.reasoning-empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.reasoning-empty-text {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin: 0;
}

/* === RESPONSIVE === */
@media (max-width: 1024px) {
  .reasoning-panel {
    width: 35%;
  }
}

@media (max-width: 768px) {
  .reasoning-panel {
    position: fixed;
    top: var(--header-height);
    right: 0;
    bottom: 0;
    width: 100%;
    max-width: 400px;
    z-index: var(--z-fixed);
    transform: translateX(100%);
    transition: transform var(--transition-normal);
  }
  
  .reasoning-panel.open {
    transform: translateX(0);
  }
  
  .reasoning-header {
    padding: var(--space-4);
  }
  
  .reasoning-content {
    padding: var(--space-4);
  }
  
  .sources-content {
    padding: var(--space-3) var(--space-4);
  }
}
