/* === KNOWLEDGE BASE === */
.knowledge-base {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--surface-secondary);
}

.knowledge-header {
  background-color: var(--surface-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6) var(--space-8);
  box-shadow: var(--shadow-sm);
}

.knowledge-header-content {
  max-width: var(--max-width-content);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-6);
}

.knowledge-title-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.knowledge-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.knowledge-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.knowledge-title-content h1 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.knowledge-title-content p {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.knowledge-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.knowledge-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-8);
}

.knowledge-content-inner {
  max-width: var(--max-width-content);
  margin: 0 auto;
}

/* === NAVIGATION TABS === */
.knowledge-nav {
  background-color: var(--surface-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: 0 var(--space-8);
  overflow-x: auto;
}

.knowledge-nav-content {
  max-width: var(--max-width-content);
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.knowledge-nav-tab {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-5);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.knowledge-nav-tab:hover {
  color: var(--text-primary);
  background-color: var(--surface-tertiary);
}

.knowledge-nav-tab.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
  background-color: var(--primary-50);
}

.knowledge-nav-tab svg {
  width: 1.125rem;
  height: 1.125rem;
}

/* === STATS GRID === */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  flex-shrink: 0;
}

.stat-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--line-height-tight);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-tight);
}

/* === SEARCH AND FILTERS === */
.search-filters-section {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.search-filters-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.search-bar {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
  background-color: var(--surface-primary);
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  width: 1.25rem;
  height: 1.25rem;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.filter-select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 140px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
}

/* === AGENT GRID === */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.agent-card {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-fast);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-200);
}

.agent-card-header {
  padding: var(--space-6);
  background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--primary-50) 100%);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.agent-avatar {
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
  overflow: hidden;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--line-height-tight);
}

.agent-model {
  display: inline-block;
  background-color: var(--primary);
  color: var(--text-inverse);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
}

.agent-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  margin: 0;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-left: auto;
  flex-shrink: 0;
}

.status-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: var(--radius-full);
  box-shadow: 0 0 0 2px var(--surface-primary);
}

.status-indicator.active {
  background-color: var(--success);
}

.status-indicator.inactive {
  background-color: var(--error);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.agent-card-body {
  padding: var(--space-6);
}

.agent-metrics {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-5);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  flex: 1;
}

.metric-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary);
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-value {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  margin: 0;
  line-height: var(--line-height-tight);
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin: 0;
  line-height: var(--line-height-tight);
}

/* === SUB-AGENTS === */
.subagents-section {
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

.subagents-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.subagents-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
}

.subagents-title svg {
  width: 1rem;
  height: 1rem;
  color: var(--secondary);
}

.subagents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
}

.subagent-card {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.subagent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--secondary), var(--tertiary));
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.subagent-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--secondary-200);
}

.subagent-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.subagent-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.subagent-info {
  flex: 1;
  min-width: 0;
}

.subagent-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--line-height-tight);
}

.subagent-model {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--surface-secondary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  margin: 0;
}

.subagent-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--space-3) 0;
}

.subagent-metrics {
  display: flex;
  gap: var(--space-3);
  font-size: var(--font-size-xs);
}

.subagent-metric {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--text-tertiary);
}

.subagent-metric svg {
  width: 0.875rem;
  height: 0.875rem;
}

/* === CREATE AGENT CARD === */
.create-agent-card {
  background: linear-gradient(135deg, var(--surface-secondary) 0%, var(--primary-50) 100%);
  border: 2px dashed var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--space-10);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  min-height: 300px;
  justify-content: center;
}

.create-agent-card:hover {
  border-color: var(--primary);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.create-agent-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.create-agent-icon svg {
  width: 2rem;
  height: 2rem;
}

.create-agent-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.create-agent-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
  max-width: 280px;
}

/* === EMPTY STATES === */
.empty-state {
  text-align: center;
  padding: var(--space-20);
  color: var(--text-secondary);
}

.empty-state-icon {
  margin-bottom: var(--space-6);
  opacity: 0.6;
}

.empty-state-icon svg {
  width: 4rem;
  height: 4rem;
  color: var(--text-tertiary);
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

.empty-state-description {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--space-6) 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* === RESPONSIVE === */
@media (max-width: 1024px) {
  .knowledge-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .knowledge-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }
  
  .agents-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .knowledge-header {
    padding: var(--space-4) var(--space-6);
  }
  
  .knowledge-content {
    padding: var(--space-6);
  }
  
  .knowledge-nav {
    padding: 0 var(--space-6);
  }
  
  .search-filters-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  
  .search-bar {
    min-width: auto;
  }
  
  .filter-controls {
    justify-content: stretch;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--space-3);
  }
  
  .stat-card {
    padding: var(--space-4);
  }
  
  .agents-grid {
    grid-template-columns: 1fr;
  }
  
  .agent-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .agent-status {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .agent-metrics {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .subagents-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .knowledge-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .knowledge-content {
    padding: var(--space-4);
  }
  
  .knowledge-nav {
    padding: 0 var(--space-4);
  }
  
  .knowledge-title-content h1 {
    font-size: var(--font-size-2xl);
  }
  
  .knowledge-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .knowledge-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .agent-avatar {
    width: 3rem;
    height: 3rem;
    font-size: var(--font-size-lg);
  }
  
  .agent-name {
    font-size: var(--font-size-lg);
  }
  
  .create-agent-card {
    padding: var(--space-8);
    min-height: 250px;
  }
  
  .create-agent-icon {
    width: 3rem;
    height: 3rem;
  }
  
  .create-agent-icon svg {
    width: 1.5rem;
    height: 1.5rem;
  }
}
