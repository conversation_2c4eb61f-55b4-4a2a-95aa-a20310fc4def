.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-section {
  display: flex;
  align-items: center;
}

.logo {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.logo-image {
  height: 36px;
  width: auto;
}

.action-buttons {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.action-button {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  margin-right: 5px;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 0.9rem;
}

.action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.action-button.active {
  color: var(--primary-color);
  font-weight: 600;
}

.action-button svg {
  margin-right: 5px;
}

.separator {
  width: 1px;
  height: 30px;
  background-color: var(--border-color);
  margin: 0 15px;
}

.agents-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.agent-icons {
  display: flex;
  align-items: center;
}

.agent-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--card-background);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.agent-icon:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
  border-color: var(--primary-color);
}

.agent-icon.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(113, 34, 255, 0.3);
}

.agent-icon.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 4px 4px 0 0;
}

.agent-icon img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.agent-icon svg {
  width: 20px;
  height: 20px;
  color: var(--text-color);
}

.agent-icon.active svg {
  color: var(--primary-color);
}

.add-agent-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(113, 34, 255, 0.1);
  border: 2px dashed var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
  cursor: pointer;
  transition: var(--transition);
}

.add-agent-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(113, 34, 255, 0.2);
}

.add-agent-btn svg {
  width: 18px;
  height: 18px;
  color: var(--primary-color);
}

.right-section {
  display: flex;
  align-items: center;
}

.theme-toggle {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: var(--transition);
  margin-right: 15px;
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 10px;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}
