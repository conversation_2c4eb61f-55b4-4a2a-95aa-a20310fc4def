/* === TOPBAR === */
.topbar {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  height: var(--header-height);
  background-color: var(--surface-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.topbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-6);
  max-width: none;
}

.topbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.topbar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
}

.topbar-logo img {
  height: 2rem;
  width: auto;
}

.topbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.topbar-nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
}

.topbar-nav-item:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.topbar-nav-item.active {
  background-color: var(--primary-50);
  color: var(--primary);
}

.topbar-nav-item svg {
  width: 1rem;
  height: 1rem;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.topbar-search {
  position: relative;
  width: 20rem;
}

.topbar-search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3) var(--space-2) var(--space-10);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.topbar-search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
}

.topbar-search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  width: 1rem;
  height: 1rem;
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.topbar-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-md);
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.topbar-action:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.topbar-action svg {
  width: 1.25rem;
  height: 1.25rem;
}

.topbar-action-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--error);
  border-radius: var(--radius-full);
  border: 2px solid var(--surface-primary);
}

.topbar-profile {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.topbar-profile:hover {
  background-color: var(--surface-tertiary);
}

.topbar-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

.topbar-profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.topbar-profile-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: 1;
}

.topbar-profile-role {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  line-height: 1;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .topbar-content {
    padding: 0 var(--space-4);
  }
  
  .topbar-search {
    display: none;
  }
  
  .topbar-nav {
    display: none;
  }
  
  .topbar-profile-info {
    display: none;
  }
  
  .topbar-left {
    gap: var(--space-4);
  }
  
  .topbar-right {
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .topbar-content {
    padding: 0 var(--space-3);
  }
  
  .topbar-logo span {
    display: none;
  }
  
  .topbar-actions {
    gap: var(--space-1);
  }
}
