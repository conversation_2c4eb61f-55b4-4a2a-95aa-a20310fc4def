:root {
  /* Colori principali */
  --primary-color: #0e66b0; /* Blu - colore principale */
  --primary-light: #2a80c8; /* Versione più chiara del blu principale */
  --primary-dark: #0a4d85; /* Versione più scura del blu principale */

  /* Colori secondari */
  --secondary-color: #009b8e; /* Verde acqua - colore secondario */
  --secondary-light: #1ab3a6; /* Versione più chiara del verde acqua */
  --secondary-dark: #007a70; /* Versione più scura del verde acqua */

  /* Colori terziari */
  --tertiary-color: #089a44; /* Verde - terzo colore */
  --tertiary-light: #25b25e; /* Versione più chiara del verde */
  --tertiary-dark: #067734; /* Versione più scura del verde */

  /* Colori di accento */
  --accent-color: #0e66b0; /* Utilizziamo il colore principale come accento */

  /* Colori di testo e sfondo */
  --text-color: #333333;
  --text-light: #666666;
  --background-color: #f5f5f7;
  --card-background: #ffffff;
  --border-color: #e0e0e0;
  --hover-background: rgba(0, 0, 0, 0.05);
  --active-background: rgba(14, 102, 176, 0.1);
  --danger-color: #e74c3c;

  /* Altre variabili di stile */
  --border-radius: 16px;
  --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  --topbar-height: 60px;
  --sidebar-width: 300px;
}

.dark-theme {
  /* Colori di testo e sfondo per dark mode */
  --text-color: #f5f5f7;
  --text-light: #b0b0b0;
  --background-color: #1a1a1a;
  --card-background: #2a2a2a;
  --border-color: #444444;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  /* Colori principali adattati per dark mode */
  --primary-light: #3a90d8; /* Versione più luminosa per dark mode */
  --secondary-light: #2ac3b6; /* Versione più luminosa per dark mode */
  --tertiary-light: #35c26e; /* Versione più luminosa per dark mode */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: var(--transition);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-color);
  margin-bottom: 1rem;
}

p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

button {
  cursor: pointer;
  border: none;
  border-radius: var(--border-radius);
  padding: 10px 20px;
  font-weight: 500;
  transition: var(--transition);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-light);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: rgba(113, 34, 255, 0.1);
}

.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Layout */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-layout {
  display: flex;
  flex: 1;
}

.sidebar {
  width: var(--sidebar-width);
  height: calc(100vh - var(--topbar-height)); /* Subtract top bar height */
  position: fixed;
  left: 0;
  top: var(--topbar-height); /* Top bar height */
  background-color: var(--card-background);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  z-index: 100;
  box-shadow: var(--shadow);
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width); /* Width of sidebar */
  padding: 0;
  margin-top: var(--topbar-height); /* Top bar height */
  display: flex;
  transition: var(--transition);
  height: calc(100vh - var(--topbar-height));
  position: relative;
  overflow: hidden;
}

.top-bar {
  height: var(--topbar-height);
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 101;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Utilities */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-5 { margin-left: 1.25rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-5 { margin-right: 1.25rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
