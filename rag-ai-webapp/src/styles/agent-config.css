/* Agent Configuration Styles */
.agent-config-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.agent-config-container h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-config-container h3::before {
  content: "";
  display: block;
  width: 4px;
  height: 24px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.config-description {
  color: var(--text-light);
  font-size: 0.9375rem;
  line-height: 1.5;
  margin: 0;
  max-width: 800px;
}

/* Config Tabs */
.config-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 24px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 4px;
  width: fit-content;
}

.config-tab {
  padding: 10px 16px;
  background: none;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.9375rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-tab:hover {
  color: var(--text-color);
  background-color: rgba(0, 0, 0, 0.03);
}

.config-tab.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(14, 102, 176, 0.2);
}

.config-tab svg {
  width: 16px;
  height: 16px;
}

/* Config Form */
.config-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.config-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  background-color: var(--background-color);
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.config-group:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.config-group label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.config-value {
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--active-background);
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 0.875rem;
}

/* Sliders */
.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  transition: var(--transition);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-light);
}

/* Select and Textarea */
.config-select,
.config-textarea {
  padding: 12px 16px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: white;
  color: var(--text-color);
  font-size: 0.9375rem;
  transition: var(--transition);
  width: 100%;
}

.config-select:focus,
.config-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(14, 102, 176, 0.1);
}

.config-textarea {
  min-height: 150px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.5;
}

/* Prompt Templates */
.prompt-templates {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.prompt-templates h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
}

.template-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.template-buttons button {
  padding: 10px 16px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: 0.9375rem;
  cursor: pointer;
  transition: var(--transition);
}

.template-buttons button:hover {
  background-color: var(--active-background);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Config Actions */
.config-actions {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.9375rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 150px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(14, 102, 176, 0.2);
}

.btn-primary:hover {
  background-color: var(--primary-dark, #0a4f8a);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(14, 102, 176, 0.3);
}

.btn-secondary {
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Full Width */
.full-width {
  grid-column: 1 / -1;
}

/* Parameter Info Tooltips */
.parameter-info {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.parameter-info:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.parameter-tooltip {
  position: absolute;
  top: -10px;
  right: 30px;
  width: 250px;
  padding: 12px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  font-size: 0.875rem;
  color: var(--text-color);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  transform: translateY(10px);
  pointer-events: none;
}

.parameter-info:hover .parameter-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .config-tabs {
    width: 100%;
    overflow-x: auto;
  }
  
  .config-actions {
    flex-direction: column;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
  
  .template-buttons {
    flex-direction: column;
  }
  
  .template-buttons button {
    width: 100%;
  }
}
