/* Add Knowledge Styles */
.add-knowledge-form {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: 24px;
  margin: 24px;
  box-shadow: var(--box-shadow);
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.source-type-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.source-type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  background-color: var(--background-color);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.source-type-option::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: transparent;
  transition: var(--transition);
}

.source-type-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.source-type-option:hover::before {
  background-color: var(--primary-color);
}

.source-type-option.active {
  border-color: var(--primary-color);
  background-color: var(--active-background);
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.source-type-option.active::before {
  background-color: var(--primary-color);
}

.source-type-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--text-color);
  transition: var(--transition);
}

.source-type-option:hover .source-type-icon,
.source-type-option.active .source-type-icon {
  color: var(--primary-color);
  background-color: var(--active-background);
}

.source-type-label {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-color);
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background-color: var(--background-color);
  margin-bottom: 24px;
}

.upload-area:hover,
.upload-area.active {
  border-color: var(--primary-color);
  background-color: var(--active-background);
}

.upload-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--text-light);
  transition: var(--transition);
}

.upload-area:hover .upload-icon,
.upload-area.active .upload-icon {
  color: var(--primary-color);
}

.upload-text {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-color);
  margin-bottom: 8px;
}

.upload-hint {
  color: var(--text-light);
  text-align: center;
  max-width: 400px;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  margin-top: 16px;
  border: 1px solid var(--border-color);
}

.file-preview-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-right: 16px;
}

.file-preview-info {
  flex: 1;
}

.file-preview-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.file-preview-meta {
  font-size: 0.875rem;
  color: var(--text-light);
}

.file-preview-remove {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.file-preview-remove:hover {
  background-color: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background-color: white;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(14, 102, 176, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--danger-color);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-dark, #0a4f8a);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(14, 102, 176, 0.2);
}

.btn-secondary {
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--hover-background);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Progress bar for upload */
.upload-progress {
  width: 100%;
  height: 8px;
  background-color: var(--background-color);
  border-radius: 4px;
  margin-top: 16px;
  overflow: hidden;
}

.upload-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.upload-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 0.875rem;
  color: var(--text-light);
}

/* URL Preview */
.url-preview {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.url-preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.url-preview-favicon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.url-preview-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.url-preview-content {
  font-size: 0.875rem;
  color: var(--text-light);
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .source-type-selector {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}
