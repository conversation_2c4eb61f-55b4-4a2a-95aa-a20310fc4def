/*
 * <PERSON><PERSON> per l'interfaccia di chat
 * Design moderno e professionale per l'interazione con l'assistente AI
 */

/* Variabili per i colori del tema */
:root {
  /* Colori principali */
  --chat-primary: #0e66b0; /* Blu - colore principale */
  --chat-primary-light: rgba(14, 102, 176, 0.1); /* Versione trasparente del blu */
  --chat-primary-dark: #0a4d85; /* Versione più scura del blu */

  /* Colori secondari */
  --chat-secondary: #009b8e; /* Verde acqua - colore secondario */
  --chat-secondary-light: rgba(0, 155, 142, 0.1); /* Versione trasparente del verde acqua */

  /* Colori terziari */
  --chat-tertiary: #089a44; /* Verde - terzo colore */

  /* Colori di accento */
  --chat-accent: #009b8e; /* Utilizziamo il colore secondario come accento */

  /* Colori di testo e sfondo */
  --chat-text: #333333;
  --chat-text-light: #666666;
  --chat-bg: #f5f5f7;
  --chat-card-bg: #ffffff;
  --chat-border: #e0e0e0;
  --chat-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --chat-transition: all 0.3s ease;

  /* Colori delle bolle di chat */
  --chat-ai-bubble: #ffffff; /* Sfondo bianco per le risposte dell'AI */
  --chat-user-bubble: #0e66b0; /* Colore principale per le bolle utente */
  --chat-user-text: #ffffff; /* Testo bianco per le bolle utente */
}

/* Variabili per la modalità dark */
.dark-theme {
  /* Colori di testo e sfondo per dark mode */
  --chat-text: #f5f5f7;
  --chat-text-light: #b0b0b0;
  --chat-bg: #1a1a1a;
  --chat-card-bg: #2a2a2a;
  --chat-border: #444444;
  --chat-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  /* Colori principali adattati per dark mode */
  --chat-primary-light: rgba(14, 102, 176, 0.2); /* Versione più visibile per dark mode */

  /* Colori delle bolle di chat per dark mode */
  --chat-ai-bubble: #2a2a2a; /* Sfondo scuro per le risposte dell'AI in dark mode */
  --chat-user-bubble: #0e66b0; /* Manteniamo lo stesso colore per le bolle utente */
}

/* Contenitore principale della chat */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--chat-bg, var(--background-color));
  position: relative;
}

/* Header della chat */
.chat-header {
  height: 70px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--chat-border, var(--border-color));
  background-color: var(--chat-card-bg, var(--card-background));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.chat-header-left {
  display: flex;
  align-items: center;
}

.chat-agent-info {
  display: flex;
  align-items: center;
}

.chat-agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--chat-primary-light);
  color: var(--chat-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 12px;
  flex-shrink: 0;
  border: 1px solid var(--chat-primary-light);
}

.chat-agent-details {
  display: flex;
  flex-direction: column;
}

.chat-agent-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--chat-text);
  margin-bottom: 2px;
}

.chat-agent-status {
  font-size: 0.75rem;
  color: #4caf50;
  display: flex;
  align-items: center;
}

.chat-agent-status::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #4caf50;
  border-radius: 50%;
  margin-right: 4px;
}

.chat-header-actions {
  display: flex;
  gap: 8px;
}

.chat-action-btn {
  background-color: var(--chat-bg);
  border: 1px solid var(--chat-border);
  color: var(--chat-text);
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  transition: var(--chat-transition);
}

.chat-action-btn:hover {
  background-color: var(--chat-primary-light);
  color: var(--chat-primary);
  border-color: var(--chat-primary-light);
}

/* Schermata di benvenuto */
.chat-welcome {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;
}

.chat-welcome-content {
  max-width: 600px;
  text-align: center;
  margin-bottom: 40px;
  align-self: center;
}

.chat-welcome-icon {
  margin-bottom: 20px;
  color: var(--chat-primary);
}

.chat-welcome-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--chat-text);
}

.chat-welcome-text {
  font-size: 1rem;
  color: var(--chat-text-light);
  line-height: 1.6;
}

.chat-suggestions-wrapper {
  width: 100%;
  max-width: 800px;
  align-self: center;
}

.chat-suggestions-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--chat-text);
  text-align: center;
  position: relative;
  padding-bottom: 10px;
}

.chat-suggestions-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: var(--chat-primary);
  border-radius: 3px;
}

.chat-suggestion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  width: 100%;
  margin: 0 auto;
}

.chat-suggestion {
  background-color: var(--chat-card-bg);
  border: 1px solid var(--chat-border);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: var(--chat-transition);
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chat-suggestion:hover {
  border-color: var(--chat-primary);
  box-shadow: 0 4px 12px rgba(113, 34, 255, 0.1);
  transform: translateY(-2px);
}

.chat-suggestion-icon {
  background-color: var(--chat-primary-light);
  color: var(--chat-primary);
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.chat-suggestion-content {
  flex: 1;
}

.chat-suggestion-title {
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 6px;
  color: var(--chat-text);
}

.chat-suggestion-description {
  font-size: 0.85rem;
  color: var(--chat-text-light);
  line-height: 1.4;
}

/* Area dei messaggi */
.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message {
  display: flex;
  align-items: flex-start;
  max-width: 80%;
}

.chat-message.ai {
  align-self: flex-start;
}

.chat-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.chat-message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.chat-message-avatar.ai {
  background-color: var(--chat-primary-light);
  color: var(--chat-primary);
  margin-right: 12px;
}

.chat-message-avatar.user {
  background-color: var(--chat-primary);
  color: white;
  margin-left: 12px;
}

.chat-message-bubble {
  background-color: var(--chat-ai-bubble);
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--chat-border);
}

.chat-message.user .chat-message-bubble {
  background-color: var(--chat-user-bubble);
  color: var(--chat-user-text);
}

.chat-message-content {
  font-size: 0.95rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.chat-message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.chat-message-time {
  font-size: 0.7rem;
  color: var(--chat-text-light);
}

.chat-message.user .chat-message-time {
  color: rgba(255, 255, 255, 0.8);
}

.chat-message-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-message-bubble:hover .chat-message-actions {
  opacity: 1;
}

.chat-action-btn {
  background: none;
  border: none;
  padding: 3px;
  border-radius: 4px;
  color: var(--chat-text-light);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chat-action-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--chat-primary);
}

.chat-action-btn.active {
  color: var(--chat-primary);
}

/* Pulsante di registrazione attivo */
.chat-tool-btn.recording {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff3b30;
  border-color: rgba(255, 0, 0, 0.3);
  animation: pulse 1.5s infinite;
}

.chat-tool-btn.recording .tool-label {
  color: #ff3b30;
}

.chat-tool-btn.recording:hover {
  background-color: rgba(255, 0, 0, 0.15);
  color: #ff3b30;
  border-color: rgba(255, 0, 0, 0.4);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}

/* Area di input */
.chat-input-area {
  padding: 16px;
  border-top: 1px solid var(--chat-border);
  background-color: var(--chat-card-bg);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-input-tools {
  display: flex;
  gap: 12px;
  padding: 0 8px;
  margin-bottom: 12px;
}

.chat-tool-btn {
  background-color: var(--chat-bg);
  border: 1px solid var(--chat-border);
  color: var(--chat-text-light);
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--chat-transition);
  font-size: 0.85rem;
  font-weight: 500;
}

.chat-tool-btn:hover {
  background-color: var(--chat-primary-light);
  color: var(--chat-primary);
  border-color: var(--chat-primary-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(113, 34, 255, 0.1);
}

.tool-label {
  display: inline-block;
}

.chat-input-container {
  display: flex;
  align-items: center;
  background-color: var(--chat-bg);
  border: 1px solid var(--chat-border);
  border-radius: 24px;
  padding: 10px 16px;
  transition: var(--chat-transition);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input-container:focus-within {
  border-color: var(--chat-primary);
  box-shadow: 0 0 0 2px var(--chat-primary-light), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.chat-input {
  flex: 1;
  border: none;
  background: none;
  padding: 8px 0;
  font-size: 0.95rem;
  color: var(--chat-text);
  resize: none;
  max-height: 120px;
  outline: none;
  line-height: 1.5;
}

.chat-input::placeholder {
  color: var(--chat-text-light);
}

.chat-send-btn {
  background-color: var(--chat-primary);
  color: white;
  border: none;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--chat-transition);
  margin-left: 8px;
  box-shadow: 0 2px 6px rgba(113, 34, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.chat-send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  border-radius: 50%;
}

.chat-send-btn:hover {
  background-color: var(--chat-primary-dark);
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(113, 34, 255, 0.3);
}

.send-icon {
  font-size: 24px;
  color: white;
  display: block;
}

.chat-send-btn:active {
  transform: scale(0.95);
}

.chat-send-btn:disabled {
  background-color: var(--text-light);
  cursor: not-allowed;
}

.chat-message {
  display: flex;
  margin-bottom: 20px;
}

.chat-message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 15px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.chat-message-avatar.user {
  background-color: #2a9d8f;
}

.chat-message-content {
  flex: 1;
  max-width: calc(100% - 55px);
}

.chat-message-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.chat-message-name {
  font-weight: 600;
  margin-right: 10px;
}

.chat-message-time {
  font-size: 0.8rem;
  color: var(--text-light);
}

.chat-message-text {
  background-color: var(--card-background);
  padding: 18px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  line-height: 1.6;
  position: relative;
}

.chat-message.ai .chat-message-text {
  border-left: 4px solid var(--primary-color);
  background-color: rgba(113, 34, 255, 0.03);
}

.chat-message.user .chat-message-text {
  border-left: 4px solid #2a9d8f;
  background-color: rgba(42, 157, 143, 0.03);
}

.chat-sources {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(113, 34, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(113, 34, 255, 0.1);
}

.chat-sources-title {
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.chat-sources-title i,
.chat-sources-title svg {
  margin-right: 5px;
}

.chat-sources-content {
  margin-top: 10px;
}

.chat-source-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: var(--border-radius);
  margin-bottom: 5px;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
}

.chat-source-icon {
  margin-right: 10px;
  color: var(--primary-color);
}

.chat-source-title {
  font-weight: 500;
  flex: 1;
}

.chat-source-relevance {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: #e9ecef;
  color: #495057;
}

.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 0 20px;
}

.chat-welcome-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.chat-welcome-title {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.chat-welcome-text {
  max-width: 600px;
  margin-bottom: 30px;
}

.chat-suggestions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  max-width: 800px;
}

.chat-suggestion {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 15px;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
  width: 100%;
  max-width: 350px;
}

.chat-suggestion:hover {
  border-color: var(--primary-color);
  box-shadow: 0 5px 15px rgba(113, 34, 255, 0.1);
}

.chat-suggestion-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--primary-color);
}

.chat-suggestion-text {
  font-size: 0.9rem;
  color: var(--text-light);
}
