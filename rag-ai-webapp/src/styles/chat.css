/* === CHAT INTERFACE === */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--surface-primary);
  border-right: 1px solid var(--border-primary);
  overflow: hidden;
}

.chat-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-agent-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.chat-agent-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--text-inverse);
}

.chat-agent-details {
  display: flex;
  flex-direction: column;
}

.chat-agent-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.chat-agent-status {
  font-size: var(--font-size-sm);
  color: var(--success);
  margin: 0;
}

.chat-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.chat-action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--surface-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.chat-action-btn:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
  border-color: var(--border-secondary);
}

.chat-action-btn svg {
  width: 1rem;
  height: 1rem;
}

/* === CHAT MESSAGES === */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: var(--space-20);
}

.chat-welcome-icon {
  margin-bottom: var(--space-6);
  color: var(--text-tertiary);
}

.chat-welcome-icon svg {
  width: 4rem;
  height: 4rem;
}

.chat-welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.chat-welcome-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 500px;
  margin: 0 0 var(--space-8) 0;
}

.chat-suggestions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
  width: 100%;
  max-width: 800px;
}

.suggestion-card {
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.suggestion-card:hover {
  background-color: var(--primary-50);
  border-color: var(--primary-200);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.suggestion-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.suggestion-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

/* === MESSAGE BUBBLES === */
.chat-message {
  display: flex;
  gap: var(--space-3);
  max-width: 100%;
}

.chat-message.user {
  flex-direction: row-reverse;
}

.chat-message-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-inverse);
  flex-shrink: 0;
}

.chat-message.user .chat-message-avatar {
  background: linear-gradient(135deg, var(--gray-400), var(--gray-500));
}

.chat-message-bubble {
  max-width: 70%;
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4) var(--space-5);
  box-shadow: var(--shadow-sm);
}

.chat-message.user .chat-message-bubble {
  background-color: var(--primary);
  color: var(--text-inverse);
  border-color: var(--primary);
}

.chat-message-content {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  margin: 0;
}

.chat-message.user .chat-message-content {
  color: var(--text-inverse);
}

.chat-message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-3);
  gap: var(--space-3);
}

.chat-message-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.chat-message.user .chat-message-time {
  color: rgba(255, 255, 255, 0.7);
}

.chat-message-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.chat-message:hover .chat-message-actions {
  opacity: 1;
}

.chat-message-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.chat-message-action:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.chat-message-action.active {
  color: var(--primary);
}

.chat-message-action svg {
  width: 0.875rem;
  height: 0.875rem;
}

/* === CHAT INPUT === */
.chat-input-container {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
}

.chat-input-wrapper {
  position: relative;
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  transition: all var(--transition-fast);
}

.chat-input-wrapper:focus-within {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
}

.chat-input-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.chat-input-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.chat-input-action:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.chat-input-action.active {
  background-color: var(--primary-50);
  color: var(--primary);
}

.chat-input-action svg {
  width: 1.125rem;
  height: 1.125rem;
}

.chat-input-field {
  display: flex;
  align-items: flex-end;
  gap: var(--space-3);
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  background: none;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  resize: none;
  min-height: 1.5rem;
  max-height: 8rem;
  padding: 0;
}

.chat-input::placeholder {
  color: var(--text-tertiary);
}

.chat-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  border: none;
  background-color: var(--primary);
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.chat-send-button:hover:not(:disabled) {
  background-color: var(--primary-600);
  transform: translateY(-1px);
}

.chat-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-send-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .chat-header {
    padding: var(--space-4);
  }
  
  .chat-messages {
    padding: var(--space-4);
  }
  
  .chat-input-container {
    padding: var(--space-4);
  }
  
  .chat-message-bubble {
    max-width: 85%;
  }
  
  .chat-suggestions {
    grid-template-columns: 1fr;
  }
  
  .chat-welcome {
    padding: var(--space-12);
  }
  
  .chat-welcome-title {
    font-size: var(--font-size-2xl);
  }
}
