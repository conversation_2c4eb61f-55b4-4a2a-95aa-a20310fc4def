/* Agent Tester Styles */
.agent-tester-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tester-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.tester-controls {
  display: flex;
  gap: 12px;
}

.debug-toggle {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-toggle::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--text-light);
  transition: var(--transition);
}

.debug-toggle:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.debug-toggle:hover::before {
  background-color: var(--primary-color);
}

.debug-toggle.active {
  background-color: var(--active-background);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.debug-toggle.active::before {
  background-color: var(--primary-color);
}

.tester-content {
  display: flex;
  gap: 24px;
  height: 500px;
}

.tester-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--card-background);
  box-shadow: var(--box-shadow);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: var(--background-color);
}

.chat-message {
  max-width: 80%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-message.user {
  align-self: flex-end;
}

.chat-message.ai {
  align-self: flex-start;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  padding: 0 8px;
}

.message-sender {
  font-weight: 600;
  color: var(--text-color);
}

.message-time {
  color: var(--text-light);
}

.message-content {
  padding: 12px 16px;
  border-radius: 12px;
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 0.9375rem;
  line-height: 1.5;
  white-space: pre-line;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.chat-message.user .message-content {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-top-right-radius: 4px;
}

.chat-message.ai .message-content {
  background-color: white;
  border-top-left-radius: 4px;
}

.message-sources {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 0.875rem;
  margin-top: 8px;
}

.sources-header {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
}

.sources-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: var(--background-color);
  border-radius: 6px;
}

.source-name {
  font-weight: 500;
  color: var(--text-color);
}

.source-relevance {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

.relevance-bar {
  height: 6px;
  background-color: var(--primary-color);
  border-radius: 3px;
  flex: 1;
}

.chat-input-container {
  display: flex;
  padding: 12px;
  border-top: 1px solid var(--border-color);
  background-color: var(--card-background);
  gap: 12px;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border-radius: 24px;
  border: 1px solid var(--border-color);
  resize: none;
  min-height: 24px;
  max-height: 120px;
  font-family: inherit;
  font-size: 0.9375rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--background-color);
  transition: var(--transition);
}

.chat-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(14, 102, 176, 0.1);
}

.chat-input::placeholder {
  color: var(--text-light);
}

.send-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.send-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.send-button:active {
  transform: translateY(0);
}

.send-button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.send-button svg {
  width: 20px;
  height: 20px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Debug Panel */
.tester-debug {
  width: 350px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--card-background);
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-shadow: var(--box-shadow);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.tester-debug h4 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.debug-section {
  margin-bottom: 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 16px;
  border: 1px solid var(--border-color);
}

.debug-section h5 {
  margin: 0 0 12px 0;
  font-size: 0.9375rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.debug-section h5::before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.debug-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-light);
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
}

.reasoning-steps {
  padding-left: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  list-style: none;
}

.reasoning-step {
  font-size: 0.875rem;
  color: var(--text-color);
  padding: 8px 12px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  position: relative;
}

.reasoning-step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.reasoning-step-content {
  flex: 1;
  line-height: 1.5;
}

.reasoning-step::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 24px;
  transform: translateX(-50%);
  width: 2px;
  height: 12px;
  background-color: var(--border-color);
}

.reasoning-step:last-child::after {
  display: none;
}

.debug-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.config-label {
  font-size: 0.875rem;
  color: var(--text-color);
  font-weight: 500;
}

.config-value {
  font-size: 0.875rem;
  color: var(--primary-color);
  font-weight: 600;
  font-family: 'Roboto Mono', monospace;
  background-color: var(--active-background);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .tester-content {
    flex-direction: column;
    height: auto;
  }

  .tester-chat {
    height: 400px;
  }

  .tester-debug {
    width: 100%;
  }
}
