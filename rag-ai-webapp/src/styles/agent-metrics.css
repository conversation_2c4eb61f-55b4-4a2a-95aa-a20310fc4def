/* Agent Metrics Styles */
.agent-metrics-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metrics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metrics-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.time-range-selector {
  display: flex;
  gap: 8px;
}

.time-range-btn {
  padding: 6px 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--text-light);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.time-range-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.time-range-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.metrics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 16px;
  text-align: center;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-light);
}

.metrics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-container {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 16px;
  border: 1px solid var(--border-color);
}

.chart-container h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.chart {
  height: 200px;
  position: relative;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  height: 180px;
  gap: 2px;
  padding-bottom: 20px;
}

.chart-bar-group {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.chart-bar {
  width: 80%;
  max-width: 20px;
  border-radius: 4px 4px 0 0;
  transition: var(--transition);
}

.chart-bar.queries {
  background-color: var(--primary-color);
}

.chart-bar.response-time {
  background-color: var(--secondary-color);
}

.chart-labels {
  display: flex;
  height: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.chart-label {
  flex: 1;
  text-align: center;
  font-size: 0.7rem;
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: var(--text-light);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.queries {
  background-color: var(--primary-color);
}

.legend-color.response-time {
  background-color: var(--secondary-color);
}

.knowledge-usage {
  margin-top: 24px;
}

.knowledge-usage h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.knowledge-table {
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.knowledge-table-header {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr;
  background-color: var(--background-color);
  padding: 12px 16px;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
}

.knowledge-table-row {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr;
  padding: 12px 16px;
  font-size: 0.875rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-background);
  transition: var(--transition);
}

.knowledge-table-row:last-child {
  border-bottom: none;
}

.knowledge-table-row:hover {
  background-color: var(--hover-background);
}

.knowledge-name {
  font-weight: 500;
  color: var(--text-color);
}

.knowledge-usage {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usage-bar-container {
  flex: 1;
  height: 8px;
  background-color: var(--background-color);
  border-radius: 4px;
  overflow: hidden;
}

.usage-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.knowledge-relevance {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

.relevance-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.no-knowledge {
  padding: 24px;
  text-align: center;
  color: var(--text-light);
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .metrics-charts {
    grid-template-columns: 1fr;
  }
  
  .metrics-summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .knowledge-table-header,
  .knowledge-table-row {
    grid-template-columns: 1.5fr 2fr 1fr;
  }
}
