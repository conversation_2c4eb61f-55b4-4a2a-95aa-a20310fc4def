/* === SETTINGS === */
.settings-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--surface-secondary);
  overflow: hidden;
}

.settings-header {
  background-color: var(--surface-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-6) var(--space-8);
  box-shadow: var(--shadow-sm);
}

.settings-header-content {
  max-width: var(--max-width-content);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-6);
}

.settings-title-section {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.settings-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.settings-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.settings-title-content h1 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.settings-title-content p {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-8);
}

.settings-content-inner {
  max-width: var(--max-width-content);
  margin: 0 auto;
}

/* === SETTINGS SECTIONS === */
.settings-section {
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-8);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.settings-section-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
}

.settings-section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.settings-section-title svg {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary);
}

.settings-section-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-normal);
}

.settings-section-body {
  padding: var(--space-6);
}

/* === FORM ELEMENTS === */
.settings-form-group {
  margin-bottom: var(--space-6);
}

.settings-form-group:last-child {
  margin-bottom: 0;
}

.settings-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.settings-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
}

.settings-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
  background-color: var(--surface-primary);
}

.settings-textarea {
  min-height: 100px;
  resize: vertical;
}

.settings-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.settings-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
}

.settings-checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.settings-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background-color: var(--surface-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.settings-checkbox:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.settings-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.settings-checkbox-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  cursor: pointer;
}

.settings-help-text {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-2);
  line-height: var(--line-height-normal);
}

/* === INTEGRATION CARDS === */
.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.integration-card {
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.integration-card:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.integration-card.connected {
  border-color: var(--success);
  background-color: rgba(16, 185, 129, 0.05);
}

.integration-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.integration-logo {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  flex-shrink: 0;
}

.integration-logo img {
  width: 2rem;
  height: 2rem;
  object-fit: contain;
}

.integration-info {
  flex: 1;
}

.integration-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.integration-status {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.integration-status.connected {
  color: var(--success);
}

.integration-status.disconnected {
  color: var(--text-tertiary);
}

.integration-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--space-4) 0;
}

.integration-actions {
  display: flex;
  gap: var(--space-3);
}

/* === ACTION BUTTONS === */
.settings-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding-top: var(--space-6);
  border-top: 1px solid var(--border-primary);
  margin-top: var(--space-6);
}

.settings-actions.sticky {
  position: sticky;
  bottom: 0;
  background-color: var(--surface-primary);
  margin: 0 -var(--space-6) -var(--space-6) -var(--space-6);
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* === RESPONSIVE === */
@media (max-width: 1024px) {
  .settings-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }
  
  .integrations-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .settings-header {
    padding: var(--space-4) var(--space-6);
  }
  
  .settings-content {
    padding: var(--space-6);
  }
  
  .settings-section-header {
    padding: var(--space-4);
  }
  
  .settings-section-body {
    padding: var(--space-4);
  }
  
  .integrations-grid {
    grid-template-columns: 1fr;
  }
  
  .integration-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .integration-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .settings-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .settings-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .settings-content {
    padding: var(--space-4);
  }
  
  .settings-title-content h1 {
    font-size: var(--font-size-2xl);
  }
  
  .settings-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .settings-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}
