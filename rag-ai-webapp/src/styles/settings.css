.settings-container {
  padding: 20px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.settings-title {
  font-size: 1.5rem;
  font-weight: 600;
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 30px;
}

.settings-tab {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  transition: var(--transition);
}

.settings-tab.active {
  border-bottom: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.settings-tab:hover:not(.active) {
  border-bottom: 2px solid var(--border-color);
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.settings-section-description {
  color: var(--text-light);
  margin-bottom: 20px;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.integration-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.integration-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
  border-color: var(--primary-color);
}

.integration-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.integration-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 1.5rem;
}

.integration-status {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.integration-status.connected {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.integration-status.disconnected {
  background-color: rgba(189, 195, 199, 0.2);
  color: #7f8c8d;
}

.integration-content {
  padding: 0 20px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.integration-title {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.integration-description {
  color: var(--text-light);
  margin-bottom: 15px;
}

.integration-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
}

.integration-configure {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: var(--transition);
}

.integration-configure:hover {
  color: var(--primary-light);
}

.integration-toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.integration-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.integration-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: var(--transition);
  border-radius: 20px;
}

.integration-toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: var(--transition);
  border-radius: 50%;
}

.integration-toggle input:checked + .integration-toggle-slider {
  background-color: var(--primary-color);
}

.integration-toggle input:checked + .integration-toggle-slider:before {
  transform: translateX(20px);
}

.add-integration-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  height: 100%;
}

.add-integration-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-3px);
}

.add-integration-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(113, 34, 255, 0.1);
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 20px;
}

.add-integration-title {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.add-integration-text {
  color: var(--text-light);
  margin-bottom: 0;
}

.settings-form {
  max-width: 600px;
}

.settings-form-group {
  margin-bottom: 20px;
}

.settings-form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.settings-form-input {
  width: 100%;
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-color);
  transition: var(--transition);
}

.settings-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(113, 34, 255, 0.2);
}

.settings-form-textarea {
  width: 100%;
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-color);
  transition: var(--transition);
  min-height: 100px;
  resize: vertical;
}

.settings-form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(113, 34, 255, 0.2);
}

.settings-form-select {
  width: 100%;
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-color);
  transition: var(--transition);
}

.settings-form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(113, 34, 255, 0.2);
}

.settings-form-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.settings-form-checkbox input {
  margin-right: 10px;
}

.settings-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 30px;
}

.settings-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: 20px;
  margin-bottom: 20px;
}

.settings-card-title {
  font-weight: 600;
  margin-bottom: 15px;
}

.settings-card-content {
  color: var(--text-light);
}

.api-key-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.api-key-input input {
  flex: 1;
}

.api-key-input button {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.api-key-input button:hover {
  color: var(--text-color);
}

.webhook-url {
  background-color: var(--background-color);
  padding: 10px;
  border-radius: var(--border-radius);
  font-family: monospace;
  margin-bottom: 10px;
  word-break: break-all;
}

.copy-button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.copy-button:hover {
  background-color: rgba(113, 34, 255, 0.1);
}
