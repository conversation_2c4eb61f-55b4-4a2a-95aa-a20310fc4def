/*
 * Stili per l'interfaccia RAG (Retrieval-Augmented Generation)
 * Un'interfaccia moderna per interagire con un sistema RAG
 */

/* Variabili per i colori del tema */
:root {
  /* Colori principali */
  --rag-primary: #0e66b0; /* Blu - colore principale */
  --rag-primary-light: rgba(14, 102, 176, 0.1); /* Versione trasparente del blu */
  --rag-primary-dark: #0a4d85; /* Versione più scura del blu */

  /* Colori secondari */
  --rag-secondary: #009b8e; /* Verde acqua - colore secondario */
  --rag-secondary-light: rgba(0, 155, 142, 0.1); /* Versione trasparente del verde acqua */

  /* Colori terziari */
  --rag-tertiary: #089a44; /* Verde - terzo colore */

  /* Colori di accento */
  --rag-accent: #009b8e; /* Utilizziamo il colore secondario come accento */

  /* Colori di testo e sfondo */
  --rag-text: #333333;
  --rag-text-light: #666666;
  --rag-bg: #f5f5f7;
  --rag-card-bg: #ffffff;
  --rag-border: #e0e0e0;
  --rag-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --rag-transition: all 0.3s ease;
}

/* Variabili per la modalità dark */
.dark-theme {
  /* Colori di testo e sfondo per dark mode */
  --rag-text: #f5f5f7;
  --rag-text-light: #b0b0b0;
  --rag-bg: #1a1a1a;
  --rag-card-bg: #2a2a2a;
  --rag-border: #444444;
  --rag-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  /* Colori principali adattati per dark mode */
  --rag-primary-light: rgba(14, 102, 176, 0.2); /* Versione più visibile per dark mode */
}

/* Layout principale dell'interfaccia RAG */
.rag-interface {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* Pannello della chat */
.rag-chat-panel {
  width: 70%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--rag-bg, var(--background-color));
  position: relative;
}

/* Contenitore della chat */
.rag-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Pannello del ragionamento */
.rag-reasoning-panel {
  width: 30%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--rag-bg, var(--background-color));
  position: relative;
  border-left: 1px solid var(--rag-border, var(--border-color));
}

/* Contenitore del ragionamento */
.rag-reasoning-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Header della chat */
.rag-chat-header {
  height: 70px; /* Uniformato a 70px come gli altri header */
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--rag-border, var(--border-color));
  background-color: var(--rag-card-bg, var(--card-background));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.rag-chat-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--rag-text, var(--text-color));
}

.rag-chat-controls {
  display: flex;
  gap: 10px;
}

.rag-chat-control-btn {
  background: none;
  border: none;
  color: var(--rag-text-light, var(--text-light));
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--rag-transition, all 0.2s ease);
}

.rag-chat-control-btn:hover {
  background-color: var(--rag-primary-light, rgba(0, 0, 0, 0.05));
  color: var(--rag-primary, var(--primary-color));
}

/* Area dei messaggi */
.rag-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Singolo messaggio */
.rag-message {
  display: flex;
  max-width: 85%;
}

.rag-message.user {
  align-self: flex-end;
}

.rag-message.ai {
  align-self: flex-start;
}

.rag-message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  flex-shrink: 0;
}

.rag-message-avatar.user {
  background-color: var(--secondary-color);
}

.rag-message-content {
  padding: 12px 16px;
  border-radius: 12px;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.5;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.rag-message.user .rag-message-content {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

/* Area di input */
.rag-input-container {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--card-background);
  display: flex;
  align-items: center;
  gap: 12px;
}

.rag-input-attachments {
  display: flex;
  gap: 8px;
}

.rag-attachment-btn {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.rag-attachment-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--primary-color);
}

.rag-input-field {
  flex: 1;
  position: relative;
}

.rag-input-textarea {
  width: 100%;
  padding: 12px 16px;
  border-radius: 24px;
  border: 1px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.5;
  resize: none;
  max-height: 120px;
  min-height: 48px;
  outline: none;
  transition: all 0.2s ease;
}

.rag-input-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(113, 34, 255, 0.1);
}

.rag-send-btn {
  background-color: var(--primary-color);
  border: none;
  color: white;
  cursor: pointer;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rag-send-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.rag-send-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Header del pannello di ragionamento */
.rag-reasoning-header {
  height: 70px; /* Uniformato a 70px come gli altri header */
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--rag-border, var(--border-color));
  background-color: var(--rag-card-bg, var(--card-background));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.rag-reasoning-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--rag-text, var(--text-color));
}

.rag-reasoning-controls {
  display: flex;
  gap: 10px;
}

.rag-reasoning-control-btn {
  background: none;
  border: none;
  color: var(--rag-text-light, var(--text-light));
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--rag-transition, all 0.2s ease);
}

.rag-reasoning-control-btn:hover {
  background-color: var(--rag-primary-light, rgba(0, 0, 0, 0.05));
  color: var(--rag-primary, var(--primary-color));
}

/* Contenuto del ragionamento */
.rag-reasoning-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  height: calc(100% - 60px); /* Altezza totale meno header */
}

/* Flusso di pensiero */
.rag-thought-flow {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 100px; /* Padding extra in fondo per assicurare la visibilità dell'ultimo elemento */
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100% - 200px); /* Spazio per il pannello delle fonti */
  position: relative;
  z-index: 1; /* Assicura che sia sotto il pannello delle fonti */
  transition: height 0.3s ease;
  scroll-padding-bottom: 100px; /* Assicura che lo scroll non nasconda l'ultimo elemento */
}

/* Elemento di spaziatura alla fine del flusso di pensiero */
.rag-thought-end-spacer {
  height: 80px; /* Altezza dello spazio vuoto alla fine aumentata */
  width: 100%;
  flex-shrink: 0;
  margin-top: 20px; /* Margine aggiuntivo sopra lo spacer */
}

/* Adatta l'altezza del flusso di pensiero quando il pannello delle fonti è espanso */
.rag-reasoning-content.sources-expanded .rag-thought-flow {
  height: calc(100% - 400px); /* Spazio per il pannello delle fonti espanso */
}

/* Singolo passaggio di pensiero */
.rag-thought-step {
  display: flex;
  align-items: flex-start;
  position: relative;
}

/* Numero del passaggio */
.rag-thought-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
  margin-right: 12px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Linea di connessione tra i passaggi */
.rag-thought-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 28px;
  left: 14px;
  width: 2px;
  height: calc(100% + 16px);
  background-color: rgba(113, 34, 255, 0.2);
  z-index: 1;
}

/* Bolla di pensiero */
.rag-thought-bubble {
  flex: 1;
  background-color: var(--rag-card-bg);
  border: 1px solid var(--rag-border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 4px; /* Piccolo margine per evitare che l'ultimo elemento sia troppo vicino al bordo */
}

/* Header della bolla di pensiero */
.rag-thought-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--rag-border);
}

.dark-theme .rag-thought-header {
  background-color: rgba(255, 255, 255, 0.02);
}

.rag-thought-title {
  font-weight: 600;
  color: var(--rag-primary);
  font-size: 0.9rem;
}

.rag-thought-time {
  font-size: 0.75rem;
  color: var(--rag-text-light);
}

/* Corpo della bolla di pensiero */
.rag-thought-body {
  padding: 12px 14px;
  color: var(--rag-text, var(--text-color));
  white-space: pre-wrap;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  border-bottom: 1px dashed var(--rag-border, var(--border-color));
}

/* Sezione degli agenti */
.rag-thought-agents {
  padding: 8px;
  background-color: rgba(14, 102, 176, 0.03);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  position: relative;
}

.dark-theme .rag-thought-agents {
  background-color: rgba(14, 102, 176, 0.08);
}

/* Box informazioni agente */
.rag-agent-info-box {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid;
  gap: 10px;
  background-color: var(--rag-card-bg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

/* Avatar dell'agente */
.rag-agent-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

/* Dettagli dell'agente */
.rag-agent-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

/* Nome dell'agente */
.rag-agent-name {
  font-weight: 600;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

/* Tool dell'agente */
.rag-agent-tool {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.75rem;
  color: var(--rag-text-light, var(--text-light));
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Icona del tool */
.rag-tool-icon {
  font-size: 0.85rem;
}

/* Nome del tool */
.rag-tool-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Pannello delle fonti */
.rag-sources-panel {
  height: 200px;
  border-top: 1px solid var(--rag-border, var(--border-color));
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--rag-card-bg, var(--card-background));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10; /* Assicura che sia sopra gli altri elementi */
  transition: height 0.3s ease;
}

/* Pannello delle fonti espanso */
.rag-sources-panel.expanded {
  height: 400px; /* Altezza quando espanso */
}

/* Header del pannello delle fonti */
.rag-sources-header {
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--rag-border, var(--border-color));
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rag-sources-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: var(--rag-text, var(--text-color));
}

/* Pulsante di espansione del pannello delle fonti */
.rag-sources-expand-btn {
  background: none;
  border: none;
  color: var(--rag-text-light, var(--text-light));
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--rag-transition, all 0.2s ease);
}

.rag-sources-expand-btn:hover {
  background-color: var(--rag-primary-light, rgba(0, 0, 0, 0.05));
  color: var(--rag-primary, var(--primary-color));
}

/* Lista delle fonti */
.rag-sources-list {
  padding: 12px;
  overflow-y: auto;
  height: calc(100% - 40px); /* Altezza totale meno header */
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Singola fonte */
.rag-source-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 8px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  position: relative;
  transition: all 0.2s ease;
}

.rag-source-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Indicatore di rilevanza */
.rag-source-relevance-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--primary-color);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.rag-source-icon {
  margin: 0 10px 0 6px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.rag-source-title {
  font-weight: 500;
  flex: 1;
  font-size: 0.85rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.rag-source-relevance {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(113, 34, 255, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(113, 34, 255, 0.2);
  font-weight: 600;
  margin-left: 8px;
}

/* Stili per il codice nei passaggi di ragionamento */
.rag-thought-body code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Roboto Mono', monospace;
}

.rag-thought-body pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}
