/* === SIDEBAR === */
.sidebar {
  width: var(--sidebar-width);
  height: calc(100vh - var(--header-height));
  background-color: var(--surface-primary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: sticky;
  top: var(--header-height);
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--surface-secondary);
}

.sidebar-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.sidebar-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

.sidebar-section {
  margin-bottom: var(--space-8);
}

.sidebar-section:last-child {
  margin-bottom: 0;
}

.sidebar-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  padding: 0 var(--space-2);
}

.sidebar-section-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-section-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-sm);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.sidebar-section-action:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.sidebar-section-action svg {
  width: 0.875rem;
  height: 0.875rem;
}

/* === CHAT ITEMS === */
.chat-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.chat-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--text-secondary);
  position: relative;
  group: hover;
}

.chat-item:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.chat-item.active {
  background-color: var(--primary-50);
  color: var(--primary);
}

.chat-item-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chat-item.active .chat-item-icon {
  background: linear-gradient(135deg, var(--primary), var(--primary-600));
}

.chat-item-icon svg {
  width: 1rem;
  height: 1rem;
  color: var(--primary);
}

.chat-item.active .chat-item-icon svg {
  color: var(--text-inverse);
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-item-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--line-height-tight);
}

.chat-item-preview {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.chat-item-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
  flex-shrink: 0;
}

.chat-item-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.chat-item-badge {
  background-color: var(--primary);
  color: var(--text-inverse);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-item-actions {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: var(--space-1);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.chat-item:hover .chat-item-actions {
  opacity: 1;
}

.chat-item-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-sm);
  border: none;
  background-color: var(--surface-primary);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.chat-item-action:hover {
  background-color: var(--surface-secondary);
  color: var(--text-primary);
}

.chat-item-action svg {
  width: 0.75rem;
  height: 0.75rem;
}

/* === FOLDERS === */
.folder-item {
  margin-bottom: var(--space-2);
}

.folder-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.folder-header:hover {
  background-color: var(--surface-tertiary);
  color: var(--text-primary);
}

.folder-icon {
  width: 1rem;
  height: 1rem;
  color: var(--text-tertiary);
  transition: transform var(--transition-fast);
}

.folder-item.expanded .folder-icon {
  transform: rotate(90deg);
}

.folder-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  flex: 1;
}

.folder-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--surface-tertiary);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
}

.folder-content {
  margin-left: var(--space-6);
  margin-top: var(--space-2);
  padding-left: var(--space-2);
  border-left: 1px solid var(--border-primary);
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: var(--header-height);
    left: 0;
    z-index: var(--z-fixed);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .sidebar-overlay {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--surface-overlay);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .sidebar-overlay.open {
    opacity: 1;
    visibility: visible;
  }
}
