/* Note: The main sidebar container properties are defined in global.css */
.sidebar-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* New chat button */
.new-chat-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px;
  margin-bottom: 20px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.new-chat-btn:hover {
  background-color: var(--primary-dark, #5c00e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(113, 34, 255, 0.2);
}

/* Section styles */
.sidebar-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-container {
  flex-shrink: 0;
}

.time-filter {
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 0.85rem;
  cursor: pointer;
}

.time-filter:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Favorites list */
.favorites-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
}

/* Chat item */
.chat-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.chat-item:hover {
  background-color: var(--hover-color);
}

.chat-item.active {
  background-color: var(--primary-color);
  color: white;
}

.chat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  flex-shrink: 0;
}

.chat-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9rem;
}

.favorite-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-light);
  opacity: 0.5;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.favorite-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  opacity: 0.8;
}

.favorite-btn.active {
  color: #FFD700;
  opacity: 1;
}

.chat-item.active .favorite-btn {
  color: rgba(255, 255, 255, 0.7);
}

.chat-item.active .favorite-btn.active {
  color: #FFD700;
}

/* Folder styles */
.folder-action-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
  padding: 10px 12px;
  margin-bottom: 12px;
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.folder-action-btn:hover {
  background-color: var(--hover-color);
}

.new-folder-input-container {
  display: flex;
  margin-bottom: 16px;
  gap: 8px;
}

.new-folder-input {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--card-background);
  color: var(--text-color);
}

.new-folder-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.new-folder-actions {
  display: flex;
  gap: 4px;
}

.new-folder-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn {
  color: var(--success-color, #4CAF50);
}

.confirm-btn:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

.cancel-btn {
  color: var(--danger-color, #F44336);
}

.cancel-btn:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

/* Folder item */
.folders-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.folder-item {
  margin-bottom: 4px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.folder-item.drag-over {
  background-color: rgba(113, 34, 255, 0.1);
}

.folder-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.folder-header:hover {
  background-color: var(--hover-color);
}

.folder-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: transform 0.2s ease;
}

.folder-toggle.open {
  transform: rotate(90deg);
}

.folder-name {
  flex: 1;
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-count {
  background-color: var(--hover-color);
  color: var(--text-light);
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.folder-content {
  margin-left: 28px;
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.folder-content.open {
  height: auto;
  margin-top: 4px;
  margin-bottom: 8px;
}

/* Dragging styles */
.chat-item.dragging {
  opacity: 0.5;
}

/* Scrollbar styles */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
