/* iCodium AI Agents - New Design System */
:root {
  --primary-color: #0e66b0;
  --secondary-color: #009b8e;
  --tertiary-color: #089a44;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --success-color: #27ae60;
  --text-color: #2c3e50;
  --text-light: #7f8c8d;
  --text-muted: #95a5a6;
  --border-color: #e8ecef;
  --border-light: #f4f6f8;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --hover-background: rgba(14, 102, 176, 0.05);
  --active-background: rgba(14, 102, 176, 0.1);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Main Container */
.agents-ai-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #e8f4f8 100%);
  font-family: var(--font-family);
}

/* Header Section */
.agents-ai-header {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  padding: 24px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.agents-ai-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.agents-ai-title h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.agents-ai-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.agents-ai-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Navigation Tabs */
.agents-ai-nav {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  padding: 0 32px;
  display: flex;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
}

.nav-tab {
  padding: 16px 24px;
  border: none;
  background: none;
  color: var(--text-light);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: var(--transition);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-tab:hover {
  color: var(--primary-color);
  background: var(--hover-background);
}

.nav-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--active-background);
}

.nav-tab svg {
  width: 18px;
  height: 18px;
}

/* Content Area */
.agents-ai-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

/* Agent Hierarchy Container */
.agent-hierarchy-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.hierarchy-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.hierarchy-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  min-width: 140px;
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-light);
  font-weight: 500;
}

/* Agent Cards */
.agent-hierarchy-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.agent-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  cursor: pointer;
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.agent-card-header {
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e8f4f8 100%);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 20px;
}

.agent-avatar {
  width: 64px;
  height: 64px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
  color: white;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 4px;
}

.agent-model {
  display: inline-block;
  background: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.agent-description {
  color: var(--text-light);
  font-size: 14px;
  line-height: 1.5;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8);
}

.status-indicator.active {
  background: var(--success-color);
}

.status-indicator.inactive {
  background: var(--danger-color);
}

.status-text {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-light);
}

/* Agent Body */
.agent-card-body {
  padding: 24px;
}

.agent-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--background-color);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-light);
}

.metric-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.metric-value {
  font-weight: 600;
  color: var(--text-color);
}

.metric-label {
  font-size: 12px;
  color: var(--text-light);
}

/* Sub-agents */
.subagents-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--border-light);
}

.subagents-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.subagents-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.subagents-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.subagent-card {
  background: var(--card-background);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius);
  padding: 20px;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.subagent-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--secondary-color);
}

.subagent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--secondary-color);
  border-radius: 4px 0 0 4px;
}

.subagent-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.subagent-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
  box-shadow: var(--shadow-sm);
}

.subagent-info {
  flex: 1;
}

.subagent-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2px;
}

.subagent-model {
  font-size: 11px;
  color: var(--text-muted);
  background: var(--background-color);
  padding: 2px 8px;
  border-radius: 12px;
}

.subagent-description {
  font-size: 13px;
  color: var(--text-light);
  line-height: 1.4;
  margin-bottom: 12px;
}

.subagent-metrics {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.subagent-metric {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
}

/* Action Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-family: var(--font-family);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--card-background);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
}

.btn-outline {
  background: transparent;
  color: var(--text-light);
  border: 1px solid var(--border-color);
}

.btn-outline:hover {
  background: var(--hover-background);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-icon {
  padding: 8px;
  min-width: 40px;
  justify-content: center;
}

/* Create Agent Card */
.create-agent-card {
  background: linear-gradient(135deg, var(--background-color) 0%, #e8f4f8 100%);
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.create-agent-card:hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--hover-background) 0%, #e8f4f8 100%);
  transform: translateY(-2px);
}

.create-agent-icon {
  width: 64px;
  height: 64px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.create-agent-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.create-agent-description {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
}

/* Knowledge Manager Styles */
.knowledge-manager-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

.knowledge-manager-header {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  padding: 24px 32px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: var(--shadow-sm);
}

.knowledge-manager-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.knowledge-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.knowledge-type-card {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.knowledge-type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: var(--transition);
}

.knowledge-type-card:hover::before,
.knowledge-type-card.active::before {
  transform: scaleX(1);
}

.knowledge-type-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.knowledge-type-card.active {
  border-color: var(--primary-color);
  background: var(--active-background);
}

.knowledge-type-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 auto 16px;
  box-shadow: var(--shadow-md);
}

.knowledge-type-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.knowledge-type-description {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
  line-height: 1.5;
}

/* Knowledge Form */
.knowledge-form-container {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.knowledge-form {
  padding: 32px;
}

.form-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.form-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-family: var(--font-family);
  transition: var(--transition);
  background: var(--card-background);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(14, 102, 176, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-color);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

/* File Upload */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 40px 20px;
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background: var(--hover-background);
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-upload-content {
  pointer-events: none;
}

.file-upload-content svg {
  color: var(--primary-color);
  margin-bottom: 16px;
}

.file-upload-content p {
  margin: 8px 0;
  color: var(--text-color);
  font-weight: 500;
}

.file-upload-hint {
  font-size: 12px;
  color: var(--text-light) !important;
  font-weight: 400 !important;
}

.uploaded-files {
  margin-top: 16px;
  padding: 16px;
  background: var(--background-color);
  border-radius: var(--border-radius-sm);
}

.uploaded-files h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--text-color);
}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-light);
  font-size: 14px;
}

.uploaded-file:last-child {
  border-bottom: none;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .agents-ai-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .agents-ai-content {
    padding: 20px;
  }

  .hierarchy-stats {
    flex-direction: column;
    gap: 12px;
  }

  .agent-metrics {
    flex-direction: column;
    gap: 12px;
  }

  .subagents-list {
    grid-template-columns: 1fr;
  }

  .agents-ai-nav {
    padding: 0 20px;
  }

  .knowledge-types-grid {
    grid-template-columns: 1fr;
  }

  .knowledge-manager-content {
    padding: 20px;
  }

  .knowledge-form {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }
}

/* CRM Integrations Styles */
.crm-integrations-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

.crm-integrations-header {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  padding: 24px 32px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: var(--shadow-sm);
}

.crm-integrations-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.integrations-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.integrations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.crm-integration-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.crm-integration-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.integration-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.integration-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.integration-info {
  flex: 1;
}

.integration-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.integration-description {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
  line-height: 1.5;
}

.integration-features {
  margin-bottom: 20px;
}

.integration-features h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 12px 0;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  background: var(--background-color);
  color: var(--text-light);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--border-light);
}

.integration-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* CRM Config Modal */
.crm-config-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.config-modal-content {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
}

.config-header {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.config-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.config-form {
  padding: 32px;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-light);
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 16px 0;
}

.form-description {
  font-size: 14px;
  color: var(--text-light);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.mapping-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.mapping-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mapping-item label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}
