/* Agent Detail Styles */
:root {
  --primary-color: #0e66b0;
  --secondary-color: #009b8e;
  --tertiary-color: #089a44;
  --danger-color: #e74c3c;
  --text-color: #333333;
  --text-light: #666666;
  --border-color: #e0e0e0;
  --background-color: #f5f5f7;
  --card-background: #ffffff;
  --hover-background: rgba(0, 0, 0, 0.05);
  --active-background: rgba(14, 102, 176, 0.1);
  --border-radius: 8px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.2s ease;
}

/* Knowledge Content */
.knowledge-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

/* Knowledge Header */
.knowledge-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
}

.knowledge-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-secondary {
  background-color: var(--background-color);
  color: var(--text-color);
}

.btn:hover {
  opacity: 0.9;
}

/* Agent Detail */
.agent-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.agent-detail-header {
  padding: 24px;
  display: flex;
  align-items: center;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
}

.agent-detail-avatar {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1.5rem;
  margin-right: 20px;
  position: relative;
  overflow: hidden;
}

.agent-detail-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.agent-detail-info {
  flex: 1;
}

.agent-detail-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.agent-detail-description {
  color: var(--text-light);
  margin-bottom: 12px;
}

.agent-detail-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.agent-detail-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-light);
  font-size: 0.875rem;
}

/* Knowledge Tabs */
.knowledge-tabs {
  display: flex;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
}

.knowledge-tab {
  padding: 16px 20px;
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.knowledge-tab:hover {
  color: var(--text-color);
}

.knowledge-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Agent Detail Content */
.agent-detail-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: var(--background-color);
}

.agent-detail-section {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--box-shadow);
}

.agent-detail-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
}

/* Knowledge Sources - Moved to knowledge-sources.css */

/* Empty State - Moved to knowledge-sources.css */

/* Agent List in Detail View */
.agent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agent-list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.agent-list-item:hover {
  background-color: var(--hover-background);
}

.agent-list-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 16px;
  position: relative;
  overflow: hidden;
}

.agent-list-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.agent-list-info {
  flex: 1;
}

.agent-list-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.agent-list-description {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 8px;
}

.agent-list-sources-count {
  font-size: 0.75rem;
  color: var(--text-light);
}

.agent-list-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.agent-list-status.active {
  background-color: rgba(8, 154, 68, 0.1);
  color: var(--tertiary-color);
}

.agent-list-status.active::before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--tertiary-color);
}

.agent-list-status.inactive {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
}

.agent-list-status.inactive::before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger-color);
}

/* Form Elements */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* Agent Graph */
.agent-graph-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.agent-graph-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.agent-graph-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.agent-graph-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--text-light);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.main-agent {
  background-color: rgba(113, 34, 255, 0.7);
}

.legend-color.sub-agent {
  background-color: rgba(113, 34, 255, 0.5);
}

.legend-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-status.active {
  background-color: #4CAF50;
}

.legend-status.inactive {
  background-color: #F44336;
}

.agent-graph-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.agent-graph-canvas-container canvas {
  width: 100%;
  height: 100%;
}

.agent-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  pointer-events: none;
  z-index: 10;
}

.agent-graph-info {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  color: var(--text-light);
  font-size: 0.875rem;
  text-align: center;
}

/* Create Agent Form */
.create-agent-form {
  padding: 24px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.model-selection-wrapper {
  margin-top: 12px;
}

.model-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.model-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.model-option:hover {
  border-color: var(--primary-color);
}

.model-option.selected {
  border-color: var(--primary-color);
  background-color: var(--active-background);
}

.model-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-icon.agata {
  background-color: rgba(14, 102, 176, 0.1);
  color: var(--primary-color);
}

.model-icon.scorpion {
  background-color: rgba(0, 155, 142, 0.1);
  color: var(--secondary-color);
}

.model-icon.maverick {
  background-color: rgba(113, 34, 255, 0.1);
  color: #7122ff;
}

.model-info {
  flex: 1;
}

.model-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
}

.model-desc {
  font-size: 0.875rem;
  color: var(--text-light);
}

.model-description-box {
  padding: 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  color: var(--text-light);
  font-size: 0.875rem;
  line-height: 1.5;
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(14, 102, 176, 0.02);
}

.upload-icon {
  color: var(--text-light);
  margin-bottom: 16px;
}

.upload-text {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 0.875rem;
  color: var(--text-light);
}

.advanced-config-toggle {
  margin: 24px 0;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  cursor: pointer;
  padding: 0;
}

.advanced-config-section {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 24px;
  margin-bottom: 24px;
}

.advanced-config-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.125rem;
  font-weight: 600;
}

.config-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 24px;
}

.config-tab {
  padding: 12px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
}

.config-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.config-group {
  margin-bottom: 24px;
}

.config-group label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.config-value {
  font-weight: normal;
  color: var(--text-light);
}

.slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: var(--border-color);
  border-radius: 3px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-light);
  margin-top: 4px;
}

.config-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: white;
}

.config-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  min-height: 100px;
  resize: vertical;
}

.full-width {
  grid-column: 1 / -1;
}

/* Error Handling */
.form-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 4px;
}

.error {
  border-color: var(--danger-color) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .model-options {
    grid-template-columns: 1fr;
  }

  .agent-detail-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .agent-detail-avatar {
    margin-bottom: 16px;
  }

  .knowledge-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
  }
}
