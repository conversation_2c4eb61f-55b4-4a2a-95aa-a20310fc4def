/* Agenti AI Styles */
:root {
  --primary-color: #0e66b0;
  --secondary-color: #009b8e;
  --tertiary-color: #089a44;
  --danger-color: #e74c3c;
  --text-color: #333333;
  --text-light: #666666;
  --border-color: #e0e0e0;
  --background-color: #f5f5f7;
  --card-background: #ffffff;
  --hover-background: rgba(0, 0, 0, 0.05);
  --active-background: rgba(14, 102, 176, 0.1);
  --border-radius: 8px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.2s ease;
}

/* Main Container */
.agents-ai-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

/* Header */
.agents-ai-header {
  padding: 24px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agents-ai-title-container {
  display: flex;
  flex-direction: column;
}

.agents-ai-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.agents-ai-subtitle {
  font-size: 0.875rem;
  color: var(--text-light);
  margin: 4px 0 0 0;
}

.agents-ai-actions {
  display: flex;
  gap: 12px;
}

.create-agent-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.create-agent-button:hover {
  opacity: 0.9;
}

/* Tabs */
.agents-ai-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
}

.tabs-container {
  display: flex;
  position: relative;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background-color: var(--background-color);
  border: none;
  border-radius: 8px;
  color: var(--text-light);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-button svg {
  width: 18px;
  height: 18px;
}

.tab-button:hover {
  background-color: var(--hover-background);
  color: var(--text-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tab-button.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(14, 102, 176, 0.3);
}

.view-options {
  display: flex;
  gap: 8px;
  background-color: var(--background-color);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.view-option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: none;
  background-color: transparent;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.view-option-button svg {
  width: 18px;
  height: 18px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.view-option-button:hover {
  background-color: var(--hover-background);
  color: var(--text-color);
}

.view-option-button.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(14, 102, 176, 0.3);
}

/* Content */
.agents-ai-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* Stats */
.agents-ai-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.stat-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: var(--box-shadow);
  flex: 1 1 0;
  min-width: 200px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-light);
}

/* Agent List */
.agent-list-container {
  width: 100%;
}

.agent-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

/* Grid View */
.agent-list.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Agent Card */
.agent-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.agent-card-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid var(--border-color);
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.agent-header-content {
  flex: 1;
}

.agent-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.agent-model {
  font-size: 0.75rem;
  color: var(--text-light);
  background-color: var(--background-color);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.agent-card-body {
  padding: 16px;
  flex: 1;
}

.agent-description {
  color: var(--text-light);
  font-size: 0.875rem;
  margin-bottom: 16px;
  line-height: 1.5;
}

.agent-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.agent-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: var(--text-light);
}

.agents-ai-status {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.agents-ai-status.active {
  background-color: var(--tertiary-color);
}

.agents-ai-status.inactive {
  background-color: var(--danger-color);
}

.agent-card-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.agent-action-button {
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 0.875rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 4px;
}

.agent-action-button:hover {
  background-color: var(--hover-background);
  color: var(--text-color);
}

/* Sub-agents */
.subagents-container {
  margin-top: 12px;
  padding-left: 24px;
  border-left: 2px dashed var(--border-color);
}

.subagent-card {
  margin-bottom: 12px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
}

.subagent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subagent-card-header {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid var(--border-color);
}

.subagent-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.subagent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.subagent-header-content {
  flex: 1;
}

.subagent-name {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.875rem;
  margin-bottom: 2px;
}

.subagent-model {
  font-size: 0.7rem;
  color: var(--text-light);
  background-color: var(--background-color);
  padding: 1px 6px;
  border-radius: 10px;
  display: inline-block;
}

.subagent-card-body {
  padding: 12px;
}

.subagent-description {
  color: var(--text-light);
  font-size: 0.8rem;
  margin-bottom: 8px;
  line-height: 1.4;
}

.subagent-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.subagent-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: var(--text-light);
}

/* Create Agent Card */
.create-agent-card {
  background-color: transparent;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  height: 100%;
}

.create-agent-card:hover {
  border-color: var(--primary-color);
  background-color: rgba(14, 102, 176, 0.02);
}

.create-agent-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.create-agent-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
}

.create-agent-description {
  color: var(--text-light);
  font-size: 0.875rem;
}

/* Agent Graph */
.agent-graph-container {
  width: 100%;
  height: 500px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  position: relative;
}

.agent-graph-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  text-align: center;
}

.agent-graph-placeholder-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.agent-graph-placeholder-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.agent-graph-placeholder-text {
  color: var(--text-light);
  max-width: 400px;
  line-height: 1.5;
}

/* Agent Detail */
.agent-detail-container {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.agent-detail-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
}

.agent-detail-avatar {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.agent-detail-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.agent-detail-info {
  flex: 1;
}

.agent-detail-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.agent-detail-model {
  font-size: 0.875rem;
  color: var(--text-light);
  background-color: var(--background-color);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 8px;
}

.agent-detail-description {
  color: var(--text-light);
  margin-bottom: 12px;
  line-height: 1.5;
}

.agent-detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.agent-detail-actions {
  display: flex;
  gap: 12px;
}

.agent-detail-button {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-detail-button.primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.agent-detail-button.secondary {
  background-color: var(--background-color);
  color: var(--text-color);
  border: none;
}

.agent-detail-button:hover {
  opacity: 0.9;
}

.agent-detail-content {
  padding: 24px;
}

.agent-detail-section {
  margin-bottom: 24px;
}

.agent-detail-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-sources-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agent-source-item {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  transition: var(--transition);
  cursor: pointer;
}

.agent-source-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.agent-source-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: var(--primary-color);
}

.agent-source-content {
  flex: 1;
}

.agent-source-name {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
}

.agent-source-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.75rem;
  color: var(--text-light);
}

/* Responsive */
@media (max-width: 768px) {
  .agents-ai-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .agents-ai-actions {
    width: 100%;
  }

  .create-agent-button {
    width: 100%;
    justify-content: center;
  }

  .agents-ai-stats {
    flex-direction: column;
  }

  .agent-list.grid-view {
    grid-template-columns: 1fr;
  }

  .agent-detail-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
