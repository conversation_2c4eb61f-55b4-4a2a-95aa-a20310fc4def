/* Knowledge Sources Styles */
.knowledge-sources-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.knowledge-sources-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.knowledge-sources-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.knowledge-sources-title svg {
  color: var(--primary-color);
}

.knowledge-sources-actions {
  display: flex;
  gap: 12px;
}

.knowledge-sources-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.filter-input {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9375rem;
  background-color: white;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--transition);
}

.filter-input svg {
  color: var(--text-light);
}

.filter-input input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 0.9375rem;
  color: var(--text-color);
  background: transparent;
}

.filter-input:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(14, 102, 176, 0.1);
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9375rem;
  background-color: white;
  color: var(--text-color);
  transition: var(--transition);
  min-width: 150px;
}

.filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(14, 102, 176, 0.1);
}

/* Knowledge Sources List */
.knowledge-sources {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.knowledge-source-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.knowledge-source-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: transparent;
  transition: var(--transition);
}

.knowledge-source-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.knowledge-source-item:hover::before {
  background-color: var(--primary-color);
}

.knowledge-source-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-right: 16px;
  flex-shrink: 0;
}

.knowledge-source-info {
  flex: 1;
  min-width: 0; /* Ensures text truncation works */
}

.knowledge-source-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.knowledge-source-description {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 8px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-source-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.75rem;
  color: var(--text-light);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.knowledge-source-type {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--active-background);
  padding: 4px 10px;
  border-radius: 16px;
  text-transform: uppercase;
  margin-left: 16px;
  flex-shrink: 0;
}

.knowledge-source-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
  opacity: 1; /* Sempre visibile */
  transition: var(--transition);
}

.source-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: white;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
  z-index: 10;
  font-size: 14px;
}

.source-action-btn:hover {
  background-color: var(--hover-background);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.source-action-btn.view-btn:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.source-action-btn.edit-btn:hover {
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.source-action-btn.delete-btn:hover {
  color: var(--danger-color);
  border-color: var(--danger-color);
}
/* Source Detail Modal */
.source-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 24px;
}

.source-detail-content {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.source-detail-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.source-detail-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.source-detail-close {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  cursor: pointer;
  transition: var(--transition);
}

.source-detail-close:hover {
  background-color: var(--hover-background);
  color: var(--text-color);
}

.source-detail-body {
  padding: 24px;
}

.source-detail-info {
  display: flex;
  margin-bottom: 24px;
}

.source-detail-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  background-color: var(--active-background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  margin-right: 20px;
  flex-shrink: 0;
}

.source-detail-meta {
  flex: 1;
}

.source-detail-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
}

.source-detail-description {
  font-size: 0.9375rem;
  color: var(--text-light);
  margin-bottom: 12px;
  line-height: 1.5;
}

.source-detail-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.source-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  color: var(--text-color);
}

.source-stat svg {
  color: var(--primary-color);
}

.source-detail-preview {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 24px;
  margin-bottom: 24px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
}

.source-detail-preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.source-detail-preview-content {
  font-size: 0.9375rem;
  color: var(--text-color);
  line-height: 1.6;
  white-space: pre-wrap;
}

.source-detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 48px 24px;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border: 1px dashed var(--border-color);
}

.empty-state-icon {
  color: var(--text-light);
  margin-bottom: 16px;
  font-size: 3rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8px;
}

.empty-state-description {
  color: var(--text-light);
  max-width: 400px;
  margin-bottom: 24px;
  line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .knowledge-sources-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .knowledge-sources-actions {
    width: 100%;
  }

  .knowledge-sources-filter {
    flex-direction: column;
    align-items: stretch;
  }

  .knowledge-source-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .knowledge-source-icon {
    margin-bottom: 16px;
    margin-right: 0;
  }

  .knowledge-source-type {
    margin-left: 0;
    margin-top: 16px;
  }

  .knowledge-source-actions {
    margin-left: 0;
    margin-top: 16px;
    width: 100%;
    justify-content: flex-end;
  }

  .source-detail-info {
    flex-direction: column;
  }

  .source-detail-icon {
    margin-bottom: 16px;
    margin-right: 0;
  }
}
