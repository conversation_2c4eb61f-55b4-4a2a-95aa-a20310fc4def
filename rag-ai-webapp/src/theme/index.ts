import { extendTheme } from '@chakra-ui/react';

// Color mode config
const config = {
  initialColorMode: 'light',
  useSystemColorMode: true,
};

// Custom colors
const colors = {
  brand: {
    500: '#0e66b0', // Primary blue
    600: '#0a4d85', // Darker blue
  },
  secondary: {
    500: '#009b8e', // Secondary teal
    600: '#007a70', // Darker teal
  },
  tertiary: {
    500: '#089a44', // Tertiary green
    600: '#067734', // Darker green
  },
};

// Create the theme
const theme = extendTheme({
  config,
  colors,
});

export default theme;
