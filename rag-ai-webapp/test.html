<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test iCodium</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0e66b0;
            text-align: center;
        }
        .status {
            background: #f0f7ff;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #0e66b0;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 iCodium Test Page</h1>
        <div class="status">
            <h3>✅ HTML Test Successful</h3>
            <p>Se vedi questa pagina, il server web funziona correttamente.</p>
            <p>Il problema potrebbe essere con React o Vite.</p>
        </div>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
