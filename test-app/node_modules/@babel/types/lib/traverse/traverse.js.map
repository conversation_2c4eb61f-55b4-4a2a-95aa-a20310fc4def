{"version": 3, "names": ["_index", "require", "traverse", "node", "handlers", "state", "enter", "exit", "traverseSimpleImpl", "ancestors", "keys", "VISITOR_KEYS", "type", "key", "subNode", "Array", "isArray", "i", "length", "child", "push", "index", "pop"], "sources": ["../../src/traverse/traverse.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions/index.ts\";\nimport type * as t from \"../index.ts\";\n\nexport type TraversalAncestors = Array<{\n  node: t.Node;\n  key: string;\n  index?: number;\n}>;\n\nexport type TraversalHandler<T> = (\n  this: undefined,\n  node: t.Node,\n  parent: TraversalAncestors,\n  state: T,\n) => void;\n\nexport type TraversalHandlers<T> = {\n  enter?: TraversalHandler<T>;\n  exit?: TraversalHandler<T>;\n};\n\n/**\n * A general AST traversal with both prefix and postfix handlers, and a\n * state object. Exposes ancestry data to each handler so that more complex\n * AST data can be taken into account.\n */\nexport default function traverse<T>(\n  node: t.Node,\n  handlers: TraversalHandler<T> | TraversalHandlers<T>,\n  state?: T,\n): void {\n  if (typeof handlers === \"function\") {\n    handlers = { enter: handlers };\n  }\n\n  const { enter, exit } = handlers;\n\n  traverseSimpleImpl(node, enter, exit, state, []);\n}\n\nfunction traverseSimpleImpl<T>(\n  node: any,\n  enter: Function | undefined,\n  exit: Function | undefined,\n  state: T | undefined,\n  ancestors: TraversalAncestors,\n) {\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return;\n\n  if (enter) enter(node, ancestors, state);\n\n  for (const key of keys) {\n    const subNode = node[key];\n\n    if (Array.isArray(subNode)) {\n      for (let i = 0; i < subNode.length; i++) {\n        const child = subNode[i];\n        if (!child) continue;\n\n        ancestors.push({\n          node,\n          key,\n          index: i,\n        });\n\n        traverseSimpleImpl(child, enter, exit, state, ancestors);\n\n        ancestors.pop();\n      }\n    } else if (subNode) {\n      ancestors.push({\n        node,\n        key,\n      });\n\n      traverseSimpleImpl(subNode, enter, exit, state, ancestors);\n\n      ancestors.pop();\n    }\n  }\n\n  if (exit) exit(node, ancestors, state);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AA0Be,SAASC,QAAQA,CAC9BC,IAAY,EACZC,QAAoD,EACpDC,KAAS,EACH;EACN,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;IAClCA,QAAQ,GAAG;MAAEE,KAAK,EAAEF;IAAS,CAAC;EAChC;EAEA,MAAM;IAAEE,KAAK;IAAEC;EAAK,CAAC,GAAGH,QAAQ;EAEhCI,kBAAkB,CAACL,IAAI,EAAEG,KAAK,EAAEC,IAAI,EAAEF,KAAK,EAAE,EAAE,CAAC;AAClD;AAEA,SAASG,kBAAkBA,CACzBL,IAAS,EACTG,KAA2B,EAC3BC,IAA0B,EAC1BF,KAAoB,EACpBI,SAA6B,EAC7B;EACA,MAAMC,IAAI,GAAGC,mBAAY,CAACR,IAAI,CAACS,IAAI,CAAC;EACpC,IAAI,CAACF,IAAI,EAAE;EAEX,IAAIJ,KAAK,EAAEA,KAAK,CAACH,IAAI,EAAEM,SAAS,EAAEJ,KAAK,CAAC;EAExC,KAAK,MAAMQ,GAAG,IAAIH,IAAI,EAAE;IACtB,MAAMI,OAAO,GAAGX,IAAI,CAACU,GAAG,CAAC;IAEzB,IAAIE,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC1B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAME,KAAK,GAAGL,OAAO,CAACG,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,EAAE;QAEZV,SAAS,CAACW,IAAI,CAAC;UACbjB,IAAI;UACJU,GAAG;UACHQ,KAAK,EAAEJ;QACT,CAAC,CAAC;QAEFT,kBAAkB,CAACW,KAAK,EAAEb,KAAK,EAAEC,IAAI,EAAEF,KAAK,EAAEI,SAAS,CAAC;QAExDA,SAAS,CAACa,GAAG,CAAC,CAAC;MACjB;IACF,CAAC,MAAM,IAAIR,OAAO,EAAE;MAClBL,SAAS,CAACW,IAAI,CAAC;QACbjB,IAAI;QACJU;MACF,CAAC,CAAC;MAEFL,kBAAkB,CAACM,OAAO,EAAER,KAAK,EAAEC,IAAI,EAAEF,KAAK,EAAEI,SAAS,CAAC;MAE1DA,SAAS,CAACa,GAAG,CAAC,CAAC;IACjB;EACF;EAEA,IAAIf,IAAI,EAAEA,IAAI,CAACJ,IAAI,EAAEM,SAAS,EAAEJ,KAAK,CAAC;AACxC", "ignoreList": []}